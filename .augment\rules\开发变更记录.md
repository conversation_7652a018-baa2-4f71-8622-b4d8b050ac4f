---
type: "manual"
---

以下是开发变更记录的规则语句：

## 开发变更记录规则

### 核心规则
**每次代码变更必须更新版本号并记录变更内容，严禁修改历史版本记录。**

### 具体要求

1. **强制更新**
   - 每次代码提交前必须更新 `C:\活码系统文档\doc\CHANGELOG.md`
   - 必须按照版本号递增规则更新版本
   - 必须记录变更类型、内容和影响范围

2. **版本号规则**
   - 主版本号：重大架构变更或不兼容更新
   - 次版本号：新功能添加或重要功能修改
   - 修订号：Bug修复、小幅优化、文档更新

3. **历史记录不可变**
   - **严禁修改**已发布版本的记录内容
   - **严禁删除**历史版本记录
   - 如需更正，只能在新版本中说明

4. **记录格式**
   ```markdown
   ## v版本号 - 版本名称
   **发布日期**: YYYY-MM-DD
   **开发者**: 姓名
   
   ### [类型] 变更内容
   - 具体变更描述
   ```

5. **违规处理**
   - 未更新版本记录的提交将被拒绝
   - 修改历史记录的行为将被回滚
   - 重复违规将影响代码审查权限

### 简化规则语句
**"每次变更必更新版本，历史记录不可改。"**

这个规则确保了开发历史的完整性和可追溯性！
