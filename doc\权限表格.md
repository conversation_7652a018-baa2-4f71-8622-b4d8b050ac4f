# 企业级活码管理系统权限表格

> **相关文档**:
> - [功能文档](功能文档.md) - 系统功能详细说明
> - [业务流程](需求与设计/业务流程.md) - 权限验证流程
> - [接口文档](前后端协作/接口文档.md) - API权限控制
> - [用户管理模块](需求与设计/活码系统设计需求.md#管理员列表及用户管理模块) - 用户管理需求

## 角色定义

| 角色 | 说明 | 级别 |
|------|------|------|
| **超级管理员** | 系统最高权限，可管理所有功能和数据 | 1级 |
| **Boss** | 可管理下属用户，查看全部业务数据 | 2级 |
| **经理** | 仅能管理自己的活码和相关数据 | 3级 |

<!--
🔧 自定义角色定义
在此处添加您的特定角色需求：
- 部门主管角色
- 区域经理角色  
- 客服专员角色
- 临时权限角色
-->

## 功能权限矩阵

| 功能模块 | 超级管理员 | Boss | 经理 |
|---------|-----------|------|------|
| **工作台** | ✅ | ✅ | ✅ |
| 查看全部统计 | ✅ | ✅ | ❌ |
| 查看个人统计 | ✅ | ✅ | ✅ |
| 查看创建者信息 | ✅ | ✅ | ❌ |
| **短码管理** | ✅ | ✅ | ✅ |
| 创建活码 | ✅ | ✅ | ✅ |
| 查看所有活码 | ✅ | ✅ | ❌ |
| 查看个人活码 | ✅ | ✅ | ✅ |
| 编辑他人活码 | ✅ | ✅ | ❌ |
| 删除他人活码 | ✅ | ✅ | ❌ |
| **链接轮询** | ✅ | ✅ | ✅ |
| 管理所有配置 | ✅ | ✅ | ❌ |
| 管理个人配置 | ✅ | ✅ | ✅ |
| **访问记录** | ✅ | ✅ | ✅ |
| 查看所有记录 | ✅ | ✅ | ❌ |
| 查看个人记录 | ✅ | ✅ | ✅ |
| 导出数据 | ✅ | ✅ | ✅ |
| **用户管理** | ✅ | ✅ | ❌ |
| 创建Boss账户 | ✅ | ❌ | ❌ |
| 创建经理账户 | ✅ | ✅ | ❌ |
| 编辑用户信息 | ✅ | ✅ | ❌ |
| 删除用户 | ✅ | ✅ | ❌ |
| 重置密码 | ✅ | ✅ | ❌ |
| **域名管理** | ✅ | ❌ | ❌ |
| 添加域名 | ✅ | ❌ | ❌ |
| 编辑域名 | ✅ | ❌ | ❌ |
| 删除域名 | ✅ | ❌ | ❌ |
| **操作日志** | ✅ | ✅ | ❌ |
| 查看所有日志 | ✅ | ❌ | ❌ |
| 查看相关日志 | ✅ | ✅ | ❌ |
| 导出日志 | ✅ | ✅ | ❌ |

<!--
🔧 功能权限自定义矩阵
在此处添加您的特定功能权限配置：

| 自定义功能 | 超级管理员 | Boss | 经理 | 备注 |
|-----------|-----------|------|------|------|
| 批量导入功能 | ✅ | ✅ | ❌ | 仅限管理层使用 |
| API密钥管理 | ✅ | ❌ | ❌ | 系统级配置 |
| 第三方集成 | ✅ | ✅ | ❌ | 需要审批 |
-->

## 数据可见性规则

### 超级管理员
- 可查看和管理系统所有数据
- 可创建和管理所有角色用户
- 拥有所有模块的完整权限

### Boss
- 可查看所有业务数据（活码、访问记录等）
- 可管理下属经理账户
- 无法访问域名管理
- 无法查看超级管理员的操作日志

### 经理
- 仅可查看和管理自己创建的数据
- 无法查看他人的活码和数据
- 无法访问用户管理、域名管理、操作日志
- 在统计数据中只显示个人相关数据

<!--
🔧 数据可见性自定义规则
在此处添加您的特定数据可见性需求：

### 部门主管（如适用）
- 可查看本部门所有数据
- 可管理本部门用户
- 跨部门数据需要申请权限

### 区域经理（如适用）  
- 可查看指定区域的数据
- 可管理区域内的活码
- 受地域限制的数据访问控制
-->

## 特殊权限说明

1. **热门短码创建者显示**：仅超级管理员和Boss可见
2. **访问记录跳转**：点击访问量可跳转到详细记录
3. **轮询数据跳转**：点击轮询数据可跳转到配置页面
4. **域名地址不可修改**：创建后仅可修改有效期和备注
5. **批次优先级自动转换**：完成后自动变为常规轮询

<!--
🔧 特殊权限自定义说明
在此处添加您的特殊权限控制需求：

6. **IP地址白名单**：管理后台访问需要在白名单内
7. **时间段限制**：某些操作仅在工作时间允许
8. **操作频率限制**：敏感操作有频率限制
9. **双重验证**：重要操作需要二次确认
10. **审批流程**：某些操作需要上级审批
-->

## 权限验证流程

1. **登录验证** → 获取用户角色
2. **路由权限** → 检查模块访问权限
3. **数据过滤** → 按角色过滤可见数据
4. **操作权限** → 实时验证操作权限
5. **审计记录** → 记录所有操作日志

<!--
🔧 权限验证流程自定义
在此处添加您的特殊验证流程：

6. **IP验证** → 检查访问来源IP
7. **设备验证** → 检查登录设备信息
8. **时间验证** → 检查操作时间限制
9. **频率验证** → 检查操作频率限制
10. **审批验证** → 检查是否需要审批
-->

## 权限继承关系

```
超级管理员 (拥有所有权限)
├── 系统配置权限
├── 所有用户管理权限
└── 所有数据访问权限
    │
    Boss (继承部分管理权限)
    ├── 下属用户管理权限
    ├── 业务数据查看权限
    └── 导出功能权限
        │
        经理 (基础操作权限)  
        ├── 个人数据管理权限
        ├── 活码创建权限
        └── 个人统计查看权限
```

<!--
🔧 权限继承关系自定义
在此处添加您的特定权限继承结构：

```
您的自定义角色层级
├── 特定权限1
├── 特定权限2
└── 特定权限3
```
-->

## 敏感操作权限控制

| 操作类型 | 超级管理员 | Boss | 经理 | 额外验证 |
|---------|-----------|------|------|----------|
| 删除用户 | ✅ | ✅ (下属) | ❌ | 二次确认 |
| 批量删除活码 | ✅ | ✅ | ❌ | 输入确认码 |
| 导出敏感数据 | ✅ | ✅ | ❌ | 审批流程 |
| 修改域名配置 | ✅ | ❌ | ❌ | 双重验证 |
| 重置用户密码 | ✅ | ✅ (下属) | ❌ | 短信验证 |

<!--
🔧 敏感操作权限自定义控制
在此处添加您的敏感操作控制需求：

| 自定义敏感操作 | 超级管理员 | Boss | 经理 | 额外验证 |
|---------------|-----------|------|------|----------|
| 系统备份 | ✅ | ❌ | ❌ | 多重验证 |
| 配置修改 | ✅ | ❌ | ❌ | 审批+验证 |
| 数据迁移 | ✅ | ❌ | ❌ | 专人操作 |
-->

## 注意事项

- 所有权限变更会记录在操作日志中
- 数据删除操作需要二次确认
- 关联数据删除会显示影响范围警告
- 分页功能在所有列表页面都可用
- 搜索和过滤遵循权限范围限制

<!--
🔧 注意事项自定义补充
在此处添加您的特殊注意事项：

- 跨部门数据访问需要申请
- 敏感时间段操作受限
- 异地登录需要验证
- 批量操作有数量限制
- 导出数据有时间限制
-->

## 权限申请流程

当用户需要额外权限时，可通过以下流程申请：

1. **提交申请** → 填写权限申请表单
2. **上级审批** → 直属上级进行审批
3. **系统管理员确认** → 最终权限配置
4. **权限生效** → 记录日志并通知用户
5. **定期审查** → 定期检查权限使用情况

<!--
🔧 权限申请流程自定义
在此处添加您的特定申请流程：

6. **业务部门审核** → 业务相关性审核
7. **安全部门审核** → 安全风险评估
8. **临时权限管理** → 临时权限的申请和回收
9. **紧急权限机制** → 紧急情况下的权限开通
10. **权限审计** → 定期的权限使用审计
-->

---

**📝 使用说明：** 本权限表格中标注 "🔧 自定义" 的区域是为您预留的，可以根据具体的组织结构和业务需求添加相应的权限配置。请确保权限设置符合您的安全要求和业务流程。