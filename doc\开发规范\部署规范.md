# 短链接系统 - 部署规范

## 一、环境规范

### 1. 环境分离
- **开发环境 (dev)**
  - 用途：日常开发和功能验证
  - 配置：最小化资源配置，可使用内存数据库
  - 更新频率：随开发进度实时更新

- **测试环境 (test)**
  - 用途：QA测试和功能验证
  - 配置：与生产环境相似但规模较小
  - 更新频率：稳定版本发布后更新

- **预发布环境 (staging)**
  - 用途：模拟生产环境，进行最终验证
  - 配置：与生产环境完全一致
  - 更新频率：生产发布前更新

- **生产环境 (prod)**
  - 用途：面向真实用户的服务
  - 配置：高可用性、高性能配置
  - 更新频率：经过充分测试后定期更新

### 2. 配置管理
- **配置文件分离**
  - 开发配置：`application-dev.yml`
  - 测试配置：`application-test.yml`
  - 预发布配置：`application-staging.yml`
  - 生产配置：`application-prod.yml`

- **敏感配置处理**
  - 数据库密码、API密钥等敏感信息使用环境变量注入
  - 生产环境禁止硬编码敏感信息
  - 敏感配置使用加密存储

- **配置中心**
  - 生产环境考虑使用配置中心（如Spring Cloud Config）
  - 配置变更需要审核流程
  - 配置历史版本管理

## 二、容器化部署规范

### 1. Docker镜像规范
- **镜像分层**
  - 基础镜像选择：
    - 前端：`nginx:alpine`
    - 后端：`eclipse-temurin:17-jre-alpine`
  - 合理利用缓存层，减少镜像大小

- **镜像标记**
  - 格式：`{项目名}:{版本号}-{环境}`
  - 示例：`shortlink-frontend:1.0.0-prod`
  - 不使用`latest`标签，明确指定版本

- **安全扫描**
  - 构建流程集成漏洞扫描
  - 禁止使用存在高危漏洞的基础镜像
  - 定期更新基础镜像

### 2. Docker Compose配置
- **服务定义**
  ```yaml
  version: '3.8'
  
  services:
    frontend:
      image: shortlink-frontend:${VERSION}-${ENV}
      restart: always
      depends_on:
        - backend
      ports:
        - "${FRONTEND_PORT}:80"
      networks:
        - shortlink-network
  
    backend:
      image: shortlink-backend:${VERSION}-${ENV}
      restart: always
      depends_on:
        - mysql
        - redis
      environment:
        - SPRING_PROFILES_ACTIVE=${ENV}
        - DB_HOST=mysql
        - DB_PASSWORD=${DB_PASSWORD}
        - REDIS_HOST=redis
      networks:
        - shortlink-network
  
    mysql:
      image: mysql:8.0
      restart: always
      volumes:
        - mysql_data:/var/lib/mysql
      environment:
        - MYSQL_ROOT_PASSWORD=${DB_ROOT_PASSWORD}
        - MYSQL_DATABASE=shortlink
      networks:
        - shortlink-network
  
    redis:
      image: redis:alpine
      restart: always
      volumes:
        - redis_data:/data
      networks:
        - shortlink-network
  
  networks:
    shortlink-network:
  
  volumes:
    mysql_data:
    redis_data:
  ```

- **环境变量管理**
  - 使用`.env`文件存储非敏感环境变量
  - 敏感环境变量通过CI/CD系统注入
  - 不同环境使用不同的环境变量文件

## 三、CI/CD流程规范

### 1. 持续集成 (CI)
- **触发条件**
  - 代码推送到开发分支
  - 创建Pull Request
  - 手动触发

- **CI流程**
  1. 代码检出
  2. 安装依赖
  3. 代码质量检查（Lint）
  4. 单元测试
  5. 构建应用
  6. 构建Docker镜像
  7. 镜像安全扫描
  8. 推送镜像到仓库

- **CI配置 (GitHub Actions)**
  ```yaml
  name: CI Pipeline
  
  on:
    push:
      branches: [ develop ]
    pull_request:
      branches: [ develop, main ]
  
  jobs:
    backend-build:
      runs-on: ubuntu-latest
      steps:
        - uses: actions/checkout@v3
        
        - name: Set up JDK 17
          uses: actions/setup-java@v3
          with:
            distribution: 'temurin'
            java-version: '17'
            cache: 'maven'
            
        - name: Build and Test
          run: mvn clean verify
          
        - name: Build Docker image
          run: |
            docker build -t shortlink-backend:${GITHUB_SHA}-dev .
            
        - name: Push Docker image
          run: |
            echo ${{ secrets.DOCKER_PASSWORD }} | docker login -u ${{ secrets.DOCKER_USERNAME }} --password-stdin
            docker push shortlink-backend:${GITHUB_SHA}-dev
  ```

### 2. 持续部署 (CD)
- **触发条件**
  - CI流程成功完成
  - 手动审批通过
  - 代码合并到特定分支（如main分支）

- **CD流程**
  1. 确认部署环境
  2. 拉取目标版本镜像
  3. 更新环境配置
  4. 执行数据库迁移
  5. 部署应用服务
  6. 执行冒烟测试
  7. 确认部署状态

- **部署策略**
  - 开发/测试环境：直接替换部署
  - 预发布/生产环境：蓝绿部署或金丝雀发布
  - 支持快速回滚机制

- **CD配置 (GitHub Actions)**
  ```yaml
  name: CD Pipeline
  
  on:
    workflow_dispatch:
      inputs:
        environment:
          description: 'Deployment environment'
          required: true
          default: 'test'
          type: choice
          options:
            - test
            - staging
            - prod
  
  jobs:
    deploy:
      runs-on: ubuntu-latest
      environment: ${{ github.event.inputs.environment }}
      steps:
        - name: Deploy to environment
          uses: appleboy/ssh-action@master
          with:
            host: ${{ secrets.SSH_HOST }}
            username: ${{ secrets.SSH_USERNAME }}
            key: ${{ secrets.SSH_KEY }}
            script: |
              cd /path/to/project
              docker-compose pull
              docker-compose down
              docker-compose up -d
  ```

## 四、监控与日志规范

### 1. 应用监控
- **指标收集**
  - 使用Prometheus收集应用和系统指标
  - Spring Boot应用集成Micrometer
  - 前端应用集成性能监控

- **监控指标**
  - 系统指标：CPU、内存、磁盘、网络
  - JVM指标：堆内存、垃圾回收、线程数
  - 应用指标：请求数、响应时间、错误率
  - 业务指标：活码创建数、短链接访问量、用户活跃度

- **告警规则**
  - 资源使用率超过80%
  - 请求错误率超过1%
  - API响应时间超过500ms
  - 应用实例不可用

### 2. 日志管理
- **日志收集**
  - 使用ELK Stack (Elasticsearch, Logstash, Kibana)
  - 日志文件格式统一为JSON格式
  - 容器日志输出到标准输出

- **日志格式**
  ```json
  {
    "timestamp": "2023-07-18T12:34:56.789Z",
    "level": "INFO",
    "thread": "http-nio-8080-exec-1",
    "logger": "com.shortlink.controller.QRCodeController",
    "message": "Created QR code with ID: 123",
    "context": {
      "userId": 456,
      "requestId": "abc-123",
      "clientIp": "***********"
    }
  }
  ```

- **日志保留策略**
  - 开发/测试环境：保留7天
  - 预发布环境：保留30天
  - 生产环境：保留90天，重要日志备份6个月

### 3. 可视化与报表
- **监控仪表盘**
  - 使用Grafana创建系统和应用仪表盘
  - 提供实时和历史数据查看
  - 按角色定制不同仪表盘

- **业务报表**
  - 日/周/月访问量统计
  - 地域分布热力图
  - 用户活跃度趋势

## 五、备份与恢复规范

### 1. 数据备份
- **备份对象**
  - 数据库
  - 配置文件
  - 用户上传文件

- **备份策略**
  - 数据库：
    - 每日增量备份
    - 每周全量备份
  - 配置文件：版本控制或配置中心
  - 文件存储：对象存储自动备份

- **备份存储**
  - 本地备份 + 异地备份
  - 加密存储备份数据
  - 定期验证备份完整性

### 2. 灾难恢复
- **恢复目标**
  - RTO (Recovery Time Objective): < 4小时
  - RPO (Recovery Point Objective): < 24小时

- **恢复流程**
  1. 确认故障影响范围
  2. 选择合适的备份数据
  3. 恢复数据库和配置
  4. 重新部署应用服务
  5. 验证系统功能
  6. 恢复业务访问

- **恢复测试**
  - 定期进行恢复演练
  - 记录和优化恢复流程
  - 建立应急响应团队 