# 活码管理系统接口文档

## 接口规范

### 基础URL

- 开发环境：`http://localhost:8080/api`
- 测试环境：`https://test-api.example.com/api`
- 生产环境：`https://api.example.com/api`

### 请求格式

- 所有请求均使用JSON格式
- Content-Type: application/json
- 请求头需包含Authorization: Bearer {token}

### 响应格式

所有API响应均遵循以下统一格式：

```json
{
  "code": 200,           // 状态码，200表示成功，非200表示失败
  "message": "success",  // 状态描述
  "data": {              // 响应数据，失败时可能为null或包含错误详情
    // 具体数据
  },
  "timestamp": 1635739200000  // 响应时间戳
}
```

### 状态码说明

| 状态码 | 说明 |
| ------ | ---- |
| 200 | 成功 |
| 400 | 请求参数错误 |
| 401 | 未授权或token过期 |
| 403 | 权限不足 |
| 404 | 资源不存在 |
| 500 | 服务器内部错误 |

### 分页参数

分页查询接口统一使用以下参数：

| 参数名 | 类型 | 必填 | 说明 |
| ------ | ---- | ---- | ---- |
| page | Integer | 否 | 页码，从1开始，默认1 |
| size | Integer | 否 | 每页条数，默认10 |
| sort | String | 否 | 排序字段，格式：字段名,asc/desc |

分页响应格式：

```json
{
  "code": 200,
  "message": "success",
  "data": {
    "content": [],        // 数据列表
    "totalElements": 100, // 总记录数
    "totalPages": 10,     // 总页数
    "size": 10,           // 每页条数
    "number": 1,          // 当前页码
    "first": true,        // 是否第一页
    "last": false         // 是否最后一页
  },
  "timestamp": 1635739200000
}
```

---

## API接口设计

### 1. 用户认证相关
- `POST /api/auth/login`: 用户登录
  - 请求体: `{username: string, password: string}`
  - 响应: `{code: 200, message: "success", data: {token: string, user: UserInfo}}`

- `POST /api/auth/logout`: 用户登出
  - 响应: `{code: 200, message: "success", data: null}`

- `GET /api/auth/profile`: 获取当前用户信息
  - 响应: `{code: 200, message: "success", data: UserInfo}`

### 2. 用户管理相关
- `GET /api/users`: 获取用户列表
  - 查询参数: `page`, `size`, `sort`, `role`, `status`
  - 响应: `{code: 200, message: "success", data: Page<UserInfo>}`

- `POST /api/users`: 创建用户
  - 请求体: `UserCreateDTO`
  - 响应: `{code: 200, message: "success", data: UserInfo}`

- `GET /api/users/{id}`: 获取单个用户
  - 响应: `{code: 200, message: "success", data: UserInfo}`

- `PUT /api/users/{id}`: 更新用户
  - 请求体: `UserUpdateDTO`
  - 响应: `{code: 200, message: "success", data: UserInfo}`

- `DELETE /api/users/{id}`: 删除用户
  - 响应: `{code: 200, message: "success", data: null}`

### 3. 活码/短码管理
- `GET /api/qrcodes`: 获取活码列表
  - 查询参数: `page`, `size`, `sort`, `creator`, `title`
  - 响应: `{code: 200, message: "success", data: Page<QRCode>}`

- `POST /api/qrcodes`: 创建活码
  - 请求体: `QRCodeCreateDTO`
  - 响应: `{code: 200, message: "success", data: QRCode}`

- `GET /api/qrcodes/{id}`: 获取单个活码
  - 响应: `{code: 200, message: "success", data: QRCode}`

- `PUT /api/qrcodes/{id}`: 更新活码
  - 请求体: `QRCodeUpdateDTO`
  - 响应: `{code: 200, message: "success", data: QRCode}`

- `DELETE /api/qrcodes/{id}`: 删除活码
  - 响应: `{code: 200, message: "success", data: null}`

- `GET /api/qrcodes/{id}/short-urls`: 获取活码关联的短链接
  - 响应: `{code: 200, message: "success", data: List<ShortUrl>}`

- `GET /api/short-urls`: 获取短链接列表
  - 查询参数: `page`, `size`, `sort`, `qrcodeId`, `wsType`
  - 响应: `{code: 200, message: "success", data: Page<ShortUrl>}`

### 4. 访问记录相关
- `GET /api/visit-logs`: 获取访问记录
  - 查询参数: `page`, `size`, `sort`, `qrcodeId`, `startTime`, `endTime`, `ip`
  - 响应: `{code: 200, message: "success", data: Page<VisitLog>}`

- `GET /api/visit-logs/qrcode/{qrcodeId}`: 获取特定活码的访问记录
  - 查询参数: `page`, `size`, `sort`
  - 响应: `{code: 200, message: "success", data: Page<VisitLog>}`

- `DELETE /api/visit-logs`: 批量删除访问记录
  - 请求体: `{ids: number[]}`
  - 响应: `{code: 200, message: "success", data: null}`

- `DELETE /api/visit-logs/all`: 删除全部访问记录
  - 查询参数: `qrcodeId` (可选)
  - 响应: `{code: 200, message: "success", data: null}`

### 5. 域名管理相关
- `GET /api/domains`: 获取域名列表
  - 查询参数: `page`, `size`, `sort`, `status`
  - 响应: `{code: 200, message: "success", data: Page<Domain>}`

- `POST /api/domains`: 添加域名
  - 请求体: `DomainCreateDTO`
  - 响应: `{code: 200, message: "success", data: Domain}`

- `PUT /api/domains/{id}`: 更新域名
  - 请求体: `DomainUpdateDTO`
  - 响应: `{code: 200, message: "success", data: Domain}`

- `DELETE /api/domains/{id}`: 删除域名
  - 响应: `{code: 200, message: "success", data: null}`

### 6. 多类型链接轮询相关
- `GET /api/polling-data`: 获取轮询数据列表
  - 查询参数: `page`, `size`, `sort`, `type`, `qrcodeId`
  - 响应: `{code: 200, message: "success", data: Page<PollingData>}`

- `POST /api/polling-data`: 添加轮询数据
  - 请求体: `PollingDataCreateDTO`
  - 响应: `{code: 200, message: "success", data: PollingData}`

- `PUT /api/polling-data/{id}`: 更新轮询数据
  - 请求体: `PollingDataUpdateDTO`
  - 响应: `{code: 200, message: "success", data: PollingData}`

- `DELETE /api/polling-data/{id}`: 删除轮询数据
  - 响应: `{code: 200, message: "success", data: null}`

- `DELETE /api/polling-data/batch`: 批量删除轮询数据
  - 请求体: `{ids: number[]}`
  - 响应: `{code: 200, message: "success", data: null}`

- `DELETE /api/polling-data/all`: 删除全部轮询数据
  - 查询参数: `qrcodeId` (可选), `type` (可选)
  - 响应: `{code: 200, message: "success", data: null}`

### 7. 日志审计相关
- `GET /api/operation-logs`: 获取操作日志
  - 查询参数: `page`, `size`, `sort`, `operatorId`, `operatorRole`, `action`, `startTime`, `endTime`
  - 响应: `{code: 200, message: "success", data: Page<OperationLog>}`

- `GET /api/login-logs`: 获取登录日志
  - 查询参数: `page`, `size`, `sort`, `userId`, `startTime`, `endTime`
  - 响应: `{code: 200, message: "success", data: Page<LoginLog>}`

### 8. 统计分析相关
- `GET /api/stats/qrcodes`: 活码统计
  - 查询参数: `qrcodeId` (可选), `startTime`, `endTime`
  - 响应: `{code: 200, message: "success", data: QRCodeStats}`

- `GET /api/stats/users`: 经理统计
  - 查询参数: `userId` (可选), `startTime`, `endTime`
  - 响应: `{code: 200, message: "success", data: UserStats}`

- `GET /api/stats/customers`: 客户统计
  - 查询参数: `startTime`, `endTime`
  - 响应: `{code: 200, message: "success", data: CustomerStats}`

- `GET /api/stats/regions`: 地域分布统计
  - 查询参数: `qrcodeId` (可选), `startTime`, `endTime`
  - 响应: `{code: 200, message: "success", data: List<RegionStats>}`

---

## 数据交互规范

### 1. 请求规范
- **请求头**
  - `Content-Type: application/json` (POST/PUT请求)
  - `Authorization: Bearer {token}` (除登录外的所有请求)
  - `Accept-Language: zh-CN` (国际化支持)

- **请求参数**
  - 查询参数:
    - 分页: `page` (从0开始), `size` (默认10)
    - 排序: `sort=field,direction` (如 `sort=createdAt,desc`)
    - 时间范围: `startTime`, `endTime` (ISO8601格式)

- **请求体**
  - 使用JSON格式
  - 属性名使用camelCase
  - 日期字段使用ISO8601格式

### 2. 响应规范
- **基础响应格式**
  ```json
  {
    "code": 200,           // HTTP状态码
    "message": "操作成功",  // 响应消息
    "data": {              // 响应数据
      // 具体数据
    }
  }
  ```

- **分页响应格式**
  ```json
  {
    "code": 200,
    "message": "操作成功",
    "data": {
      "content": [],       // 数据列表
      "totalElements": 0,  // 总记录数
      "totalPages": 0,     // 总页数
      "size": 10,          // 每页大小
      "number": 0          // 当前页码
    }
  }
  ```

- **错误响应格式**
  ```json
  {
    "code": 400,           // HTTP状态码
    "message": "错误描述",  // 错误信息
    "data": null
  }
  ```

### 3. 状态码使用规范
- **2xx**: 成功
  - `200 OK`: 请求成功
  - `201 Created`: 资源创建成功
  - `204 No Content`: 删除成功

- **4xx**: 客户端错误
  - `400 Bad Request`: 请求参数错误
  - `401 Unauthorized`: 未认证
  - `403 Forbidden`: 权限不足
  - `404 Not Found`: 资源不存在
  - `409 Conflict`: 资源冲突（如唯一性约束）

- **5xx**: 服务器错误
  - `500 Internal Server Error`: 服务器内部错误

### 4. 安全规范
- 所有API请求必须通过HTTPS发送
- 除登录接口外，所有请求都需要携带有效的JWT令牌
- 敏感数据（如密码）不应在响应中返回
- API限流: 同一IP/用户在单位时间内的请求次数限制
- 请求参数应进行验证，防止XSS和SQL注入

### 5. 版本控制
- API版本在URL中表示: `/api/v1/users`
- 主版本号变更代表不兼容的API变化
- 次版本号变更代表向后兼容的功能性变化
- 补丁版本号变更代表向后兼容的bug修复

---

## 认证接口

### 登录

- **URL**: `/auth/login`
- **方法**: POST
- **描述**: 用户登录获取token

**请求参数**:

```json
{
  "email": "<EMAIL>",
  "password": "password123"
}
```

**响应示例**:

```json
{
  "code": 200,
  "message": "登录成功",
  "data": {
    "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
    "refreshToken": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
    "expiresIn": 3600,
    "user": {
      "id": "1",
      "name": "管理员",
      "email": "<EMAIL>",
      "role": "super-admin"
    }
  },
  "timestamp": 1635739200000
}
```

### 刷新Token

- **URL**: `/auth/refresh-token`
- **方法**: POST
- **描述**: 使用refreshToken获取新的访问token

**请求参数**:

```json
{
  "refreshToken": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..."
}
```

**响应示例**:

```json
{
  "code": 200,
  "message": "Token刷新成功",
  "data": {
    "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
    "expiresIn": 3600
  },
  "timestamp": 1635739200000
}
```

---

## 用户管理接口

### 获取用户列表

- **URL**: `/users`
- **方法**: GET
- **描述**: 获取用户列表，支持分页和筛选
- **权限**: super-admin, boss

**请求参数**:

| 参数名 | 类型 | 必填 | 说明 |
| ------ | ---- | ---- | ---- |
| page | Integer | 否 | 页码，默认1 |
| size | Integer | 否 | 每页条数，默认10 |
| name | String | 否 | 用户名称，支持模糊查询 |
| email | String | 否 | 邮箱地址，支持模糊查询 |
| role | String | 否 | 用户角色 |
| status | String | 否 | 用户状态 |

**响应示例**:

```json
{
  "code": 200,
  "message": "success",
  "data": {
    "content": [
      {
        "id": "1",
        "name": "管理员",
        "email": "<EMAIL>",
        "role": "super-admin",
        "status": "active",
        "superiorId": null,
        "createdAt": "2025-01-01T00:00:00Z",
        "updatedAt": "2025-01-01T00:00:00Z",
        "lastLoginAt": "2025-01-01T00:00:00Z"
      }
    ],
    "totalElements": 1,
    "totalPages": 1,
    "size": 10,
    "number": 1,
    "first": true,
    "last": true
  },
  "timestamp": 1635739200000
}
```

### 创建用户

- **URL**: `/users`
- **方法**: POST
- **描述**: 创建新用户
- **权限**: super-admin, boss

**请求参数**:

```json
{
  "name": "新用户",
  "email": "<EMAIL>",
  "password": "password123",
  "role": "manager",
  "superiorId": "2"
}
```

**响应示例**:

```json
{
  "code": 200,
  "message": "用户创建成功",
  "data": {
    "id": "3",
    "name": "新用户",
    "email": "<EMAIL>",
    "role": "manager",
    "status": "active",
    "superiorId": "2",
    "createdAt": "2025-01-01T00:00:00Z",
    "updatedAt": "2025-01-01T00:00:00Z"
  },
  "timestamp": 1635739200000
}
```

---

## 短码管理接口

### 获取短码列表

- **URL**: `/short-codes`
- **方法**: GET
- **描述**: 获取短码列表，支持分页和筛选

**请求参数**:

| 参数名 | 类型 | 必填 | 说明 |
| ------ | ---- | ---- | ---- |
| page | Integer | 否 | 页码，默认1 |
| size | Integer | 否 | 每页条数，默认10 |
| title | String | 否 | 短码标题，支持模糊查询 |
| shortCode | String | 否 | 短码，支持精确查询 |
| type | String | 否 | 短码类型 |
| status | String | 否 | 短码状态 |
| createdBy | String | 否 | 创建者ID |

**响应示例**:

```json
{
  "code": 200,
  "message": "success",
  "data": {
    "content": [
      {
        "id": "1",
        "title": "测试短码",
        "shortCode": "abc123",
        "type": "WhatsApp",
        "description": "这是一个测试短码",
        "status": "active",
        "visitCount": 100,
        "createdAt": "2025-01-01T00:00:00Z",
        "updatedAt": "2025-01-01T00:00:00Z",
        "createdBy": "1",
        "domainId": "1",
        "domain": {
          "id": "1",
          "domainName": "example.com",
          "status": "active"
        },
        "creator": {
          "id": "1",
          "name": "管理员"
        }
      }
    ],
    "totalElements": 1,
    "totalPages": 1,
    "size": 10,
    "number": 1,
    "first": true,
    "last": true
  },
  "timestamp": 1635739200000
}
```

### 创建短码

- **URL**: `/short-codes`
- **方法**: POST
- **描述**: 创建新短码

**请求参数**:

```json
{
  "title": "新短码",
  "type": "WhatsApp",
  "description": "这是一个新短码",
  "domainId": "1"
}
```

**响应示例**:

```json
{
  "code": 200,
  "message": "短码创建成功",
  "data": {
    "id": "2",
    "title": "新短码",
    "shortCode": "xyz789",
    "type": "WhatsApp",
    "description": "这是一个新短码",
    "status": "active",
    "visitCount": 0,
    "createdAt": "2025-01-01T00:00:00Z",
    "updatedAt": "2025-01-01T00:00:00Z",
    "createdBy": "1",
    "domainId": "1"
  },
  "timestamp": 1635739200000
}
```

---

## 域名管理接口

### 获取域名列表

- **URL**: `/domains`
- **方法**: GET
- **描述**: 获取域名列表，支持分页和筛选
- **权限**: super-admin

**请求参数**:

| 参数名 | 类型 | 必填 | 说明 |
| ------ | ---- | ---- | ---- |
| page | Integer | 否 | 页码，默认1 |
| size | Integer | 否 | 每页条数，默认10 |
| domainName | String | 否 | 域名名称，支持模糊查询 |
| status | String | 否 | 域名状态 |

**响应示例**:

```json
{
  "code": 200,
  "message": "success",
  "data": {
    "content": [
      {
        "id": "1",
        "domainName": "example.com",
        "status": "active",
        "expiredAt": "2025-01-01T00:00:00Z",
        "createdAt": "2025-01-01T00:00:00Z",
        "updatedAt": "2025-01-01T00:00:00Z",
        "createdBy": "1",
        "creator": {
          "id": "1",
          "name": "管理员"
        }
      }
    ],
    "totalElements": 1,
    "totalPages": 1,
    "size": 10,
    "number": 1,
    "first": true,
    "last": true
  },
  "timestamp": 1635739200000
}
```

---

## 链接轮询接口

### 获取批次列表

- **URL**: `/short-codes/{shortCodeId}/batches`
- **方法**: GET
- **描述**: 获取指定短码的批次列表

**请求参数**:

| 参数名 | 类型 | 必填 | 说明 |
| ------ | ---- | ---- | ---- |
| shortCodeId | String | 是 | 短码ID |
| page | Integer | 否 | 页码，默认1 |
| size | Integer | 否 | 每页条数，默认10 |
| status | String | 否 | 批次状态 |

**响应示例**:

```json
{
  "code": 200,
  "message": "success",
  "data": {
    "content": [
      {
        "id": "1",
        "name": "测试批次",
        "shortCodeId": "1",
        "linkType": "WhatsApp",
        "priority": 1,
        "priorityCount": 10,
        "usedCount": 5,
        "status": "priority",
        "createdAt": "2025-01-01T00:00:00Z",
        "updatedAt": "2025-01-01T00:00:00Z",
        "createdBy": "1",
        "creator": {
          "id": "1",
          "name": "管理员"
        }
      }
    ],
    "totalElements": 1,
    "totalPages": 1,
    "size": 10,
    "number": 1,
    "first": true,
    "last": true
  },
  "timestamp": 1635739200000
}
```

### 创建批次

- **URL**: `/short-codes/{shortCodeId}/batches`
- **方法**: POST
- **描述**: 为指定短码创建新批次

**请求参数**:

```json
{
  "name": "新批次",
  "linkType": "WhatsApp",
  "priority": 1,
  "priorityCount": 10
}
```

**响应示例**:

```json
{
  "code": 200,
  "message": "批次创建成功",
  "data": {
    "id": "2",
    "name": "新批次",
    "shortCodeId": "1",
    "linkType": "WhatsApp",
    "priority": 1,
    "priorityCount": 10,
    "usedCount": 0,
    "status": "priority",
    "createdAt": "2025-01-01T00:00:00Z",
    "updatedAt": "2025-01-01T00:00:00Z",
    "createdBy": "1"
  },
  "timestamp": 1635739200000
}
```

---

## 访问记录接口

### 获取访问记录

- **URL**: `/access-records`
- **方法**: GET
- **描述**: 获取访问记录列表，支持分页和筛选

**请求参数**:

| 参数名 | 类型 | 必填 | 说明 |
| ------ | ---- | ---- | ---- |
| page | Integer | 否 | 页码，默认1 |
| size | Integer | 否 | 每页条数，默认10 |
| shortCodeId | String | 否 | 短码ID |
| startTime | String | 否 | 开始时间，格式：yyyy-MM-dd HH:mm:ss |
| endTime | String | 否 | 结束时间，格式：yyyy-MM-dd HH:mm:ss |
| ip | String | 否 | IP地址 |
| location | String | 否 | IP位置 |

**响应示例**:

```json
{
  "code": 200,
  "message": "success",
  "data": {
    "content": [
      {
        "id": "1",
        "shortCodeId": "1",
        "shortCode": "abc123",
        "accessTime": "2025-01-01T00:00:00Z",
        "ip": "***********",
        "location": "中国北京",
        "userAgent": "Mozilla/5.0...",
        "referer": "https://example.com",
        "linkDataId": "1",
        "linkContent": "https://whatsapp.com/123456"
      }
    ],
    "totalElements": 1,
    "totalPages": 1,
    "size": 10,
    "number": 1,
    "first": true,
    "last": true
  },
  "timestamp": 1635739200000
}
```

---

## 操作日志接口

### 获取操作日志

- **URL**: `/operation-logs`
- **方法**: GET
- **描述**: 获取操作日志列表，支持分页和筛选
- **权限**: super-admin, boss

**请求参数**:

| 参数名 | 类型 | 必填 | 说明 |
| ------ | ---- | ---- | ---- |
| page | Integer | 否 | 页码，默认1 |
| size | Integer | 否 | 每页条数，默认10 |
| userId | String | 否 | 用户ID |
| operationType | String | 否 | 操作类型 |
| startTime | String | 否 | 开始时间，格式：yyyy-MM-dd HH:mm:ss |
| endTime | String | 否 | 结束时间，格式：yyyy-MM-dd HH:mm:ss |

**响应示例**:

```json
{
  "code": 200,
  "message": "success",
  "data": {
    "content": [
      {
        "id": "1",
        "userId": "1",
        "userName": "管理员",
        "userRole": "super-admin",
        "operationType": "CREATE",
        "operationObject": "SHORT_CODE",
        "objectId": "1",
        "details": "{\"title\":\"测试短码\",\"type\":\"WhatsApp\"}",
        "ip": "***********",
        "operationTime": "2025-01-01T00:00:00Z"
      }
    ],
    "totalElements": 1,
    "totalPages": 1,
    "size": 10,
    "number": 1,
    "first": true,
    "last": true
  },
  "timestamp": 1635739200000
}
```

---

## 短码跳转接口

### 短码跳转

- **URL**: `/{shortCode}`
- **方法**: GET
- **描述**: 根据短码进行跳转
- **权限**: 无需认证

**请求参数**:

| 参数名 | 类型 | 必填 | 说明 |
| ------ | ---- | ---- | ---- |
| shortCode | String | 是 | 短码，路径参数 |

**响应**:

- 302重定向到目标URL

---

## 错误码说明

| 错误码 | 说明 |
| ------ | ---- |
| 400001 | 请求参数验证失败 |
| 400002 | 请求参数格式错误 |
| 401001 | 未登录或token已过期 |
| 401002 | token无效 |
| 403001 | 权限不足 |
| 403002 | 数据访问权限不足 |
| 404001 | 资源不存在 |
| 500001 | 服务器内部错误 |
| 500002 | 数据库操作失败 |

---

## 接口调用示例

### 使用curl调用登录接口

```bash
curl -X POST http://localhost:8080/api/auth/login \
  -H "Content-Type: application/json" \
  -d '{"email":"<EMAIL>","password":"password123"}'
```

### 使用curl调用创建短码接口

```bash
curl -X POST http://localhost:8080/api/short-codes \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer {token}" \
  -d '{"title":"新短码","type":"WhatsApp","description":"这是一个新短码","domainId":"1"}'
```

---

## 注意事项

1. 所有接口调用需要在请求头中携带有效的token（除了登录、刷新token和短码跳转接口）
2. 分页接口的page参数从1开始
3. 日期时间格式统一使用ISO 8601标准：yyyy-MM-ddTHH:mm:ssZ
4. 所有请求和响应的字符编码均为UTF-8
5. 接口调用频率限制为每IP每分钟100次，超过限制将返回429状态码
6. 文件上传接口单个文件大小不超过10MB
