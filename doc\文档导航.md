# 活码管理系统文档导航

## 📖 文档使用指南

本文档导航帮助您快速找到所需的文档内容。文档按照开发流程和功能模块进行组织，建议按照以下顺序阅读：

### 🚀 新手入门路径
1. [项目概述](../README.md) - 了解项目基本信息
2. [活码系统设计需求](需求与设计/活码系统设计需求.md) - 理解业务需求
3. [项目架构](数据库与架构/项目架构.md) - 掌握技术架构
4. [权限表格](权限表格.md) - 了解权限体系
5. [开发规范](开发规范/代码规范.md) - 学习开发标准

### 👨‍💼 产品经理路径
1. [业务流程](需求与设计/业务流程.md) - 详细业务流程
2. [功能文档](功能文档.md) - 完整功能说明
3. [流程图](需求与设计/流程图.md) - 可视化流程
4. [权限表格](权限表格.md) - 权限矩阵

### 🏗️ 架构师路径
1. [项目架构](数据库与架构/项目架构.md) - 技术架构设计
2. [数据库设计](../database/README.md) - 数据库架构
3. [接口文档](前后端协作/接口文档.md) - API设计
4. [技术栈规范](开发规范/技术栈规范.md) - 技术选型

### 💻 前端开发路径
1. [项目架构](数据库与架构/项目架构.md) - 前端架构部分
2. [代码规范](开发规范/代码规范.md) - 前端代码规范
3. [界面风格指南](界面设计/网站界面风格指南.md) - UI设计规范
4. [接口文档](前后端协作/接口文档.md) - API接口
5. [模块职责划分](前后端协作/模块前后端职责划分.md) - 前端职责

### ⚙️ 后端开发路径
1. [数据库设计](../database/README.md) - 数据库结构
2. [表结构定义](../database/schema.sql) - 具体表结构
3. [接口文档](前后端协作/接口文档.md) - API规范
4. [代码规范](开发规范/代码规范.md) - 后端代码规范
5. [模块职责划分](前后端协作/模块前后端职责划分.md) - 后端职责

### 🧪 测试工程师路径
1. [测试规范](开发规范/测试规范.md) - 测试标准
2. [功能文档](功能文档.md) - 测试用例参考
3. [接口文档](前后端协作/接口文档.md) - 接口测试
4. [业务流程](需求与设计/业务流程.md) - 业务测试场景

### 🚀 运维工程师路径
1. [部署规范](开发规范/部署规范.md) - 部署标准
2. [部署上线指南](部署上线指南.md) - 具体部署步骤
3. [数据库设计](../database/README.md) - 数据库部署
4. [项目架构](数据库与架构/项目架构.md) - 系统架构

## 📂 文档分类索引

### 需求与设计类
| 文档名称 | 用途 | 目标读者 |
|---------|------|----------|
| [活码系统设计需求](需求与设计/活码系统设计需求.md) | 系统功能需求详细说明 | 全员 |
| [业务流程](需求与设计/业务流程.md) | 业务流程详细描述 | 产品、开发、测试 |
| [流程图](需求与设计/流程图.md) | 可视化业务流程 | 全员 |

### 架构设计类
| 文档名称 | 用途 | 目标读者 |
|---------|------|----------|
| [项目架构](数据库与架构/项目架构.md) | 完整技术架构设计 | 架构师、开发 |
| [数据库设计](../database/README.md) | 数据库架构说明 | 后端、DBA |
| [表结构定义](../database/schema.sql) | 具体数据库表结构 | 后端、DBA |
| [实体关系图](../database/er_diagram.md) | 数据库ER图 | 后端、DBA |

### 权限管理类
| 文档名称 | 用途 | 目标读者 |
|---------|------|----------|
| [权限表格](权限表格.md) | 详细权限矩阵 | 全员 |
| [功能文档](功能文档.md) | 功能和权限说明 | 产品、开发、测试 |

### 开发协作类
| 文档名称 | 用途 | 目标读者 |
|---------|------|----------|
| [接口文档](前后端协作/接口文档.md) | API接口规范 | 前后端开发 |
| [模块职责划分](前后端协作/模块前后端职责划分.md) | 前后端职责分工 | 前后端开发 |

### 开发规范类
| 文档名称 | 用途 | 目标读者 |
|---------|------|----------|
| [代码规范](开发规范/代码规范.md) | 代码编写标准 | 开发团队 |
| [技术栈规范](开发规范/技术栈规范.md) | 技术选型规范 | 架构师、开发 |
| [测试规范](开发规范/测试规范.md) | 测试标准流程 | 测试、开发 |
| [部署规范](开发规范/部署规范.md) | 部署运维标准 | 运维、开发 |

### 设计规范类
| 文档名称 | 用途 | 目标读者 |
|---------|------|----------|
| [界面风格指南](界面设计/网站界面风格指南.md) | UI设计规范 | 设计师、前端 |

### 项目管理类
| 文档名称 | 用途 | 目标读者 |
|---------|------|----------|
| [任务清单](项目管理/任务清单.md) | 开发任务管理 | 项目经理、开发 |
| [开发步骤与进度计划](项目管理/开发步骤与进度计划.md) | 项目进度规划 | 项目经理、开发 |

### 部署运维类
| 文档名称 | 用途 | 目标读者 |
|---------|------|----------|
| [部署上线指南](部署上线指南.md) | 部署操作指南 | 运维、开发 |

## 🔍 快速查找

### 按关键词查找
- **权限相关**: [权限表格](权限表格.md), [功能文档](功能文档.md)
- **API接口**: [接口文档](前后端协作/接口文档.md)
- **数据库**: [数据库设计](../database/README.md), [表结构](../database/schema.sql)
- **前端开发**: [项目架构](数据库与架构/项目架构.md), [代码规范](开发规范/代码规范.md)
- **后端开发**: [接口文档](前后端协作/接口文档.md), [数据库设计](../database/README.md)
- **部署运维**: [部署指南](部署上线指南.md), [部署规范](开发规范/部署规范.md)

### 按开发阶段查找
- **需求分析**: [设计需求](需求与设计/活码系统设计需求.md), [业务流程](需求与设计/业务流程.md)
- **架构设计**: [项目架构](数据库与架构/项目架构.md), [数据库设计](../database/README.md)
- **开发实现**: [代码规范](开发规范/代码规范.md), [接口文档](前后端协作/接口文档.md)
- **测试验证**: [测试规范](开发规范/测试规范.md), [功能文档](功能文档.md)
- **部署上线**: [部署指南](部署上线指南.md), [部署规范](开发规范/部署规范.md)

## 📝 文档更新记录

| 日期 | 更新内容 | 更新人 |
|------|----------|--------|
| 2025-01-31 | 创建文档导航，整合所有文档 | 系统整合 |
| 2025-01-31 | 统一技术栈和API规范 | 系统整合 |
| 2025-01-31 | 修正权限矩阵和业务流程 | 系统整合 |

---

**使用建议**: 
1. 首次接触项目请按照"新手入门路径"阅读
2. 根据角色选择对应的阅读路径
3. 使用快速查找功能定位特定内容
4. 文档间有交叉引用，建议配合阅读
