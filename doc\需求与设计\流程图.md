# 短链接系统 - 业务流程图

本文档通过流程图展示短链接系统的核心业务流程，便于理解系统运作方式。

## 1. 活码创建流程

以下流程图展示了活码创建的完整流程：

```mermaid
flowchart TD
    A(用户登录系统) --> B{权限校验}
    B -->|有权限| C(点击添加活码按钮)
    B -->|无权限| D(提示无权限)
    C --> E(填写活码信息)
    E --> F(系统生成随机码)
    F --> G(保存活码信息)
    G --> H(创建完成)
```

**流程说明**：
1. 用户登录系统后进入活码管理界面
2. 系统校验用户创建活码的权限
3. 权限验证通过后，用户点击"添加活码"按钮
4. 用户填写活码信息（标题、域名选择等）
5. 系统自动生成随机码（不可编辑）
6. 系统保存活码信息及随机码
7. 活码创建完成，可用于后续分发

## 2. 短链接访问流程

以下流程图展示了外部用户访问短链接的流程：

```mermaid
flowchart TD
    I(外部访问者点击短链接) --> J(系统提取URL中的随机码)
    J --> K{校验随机码有效性}
    K -->|有效| L(记录访问信息)
    K -->|无效| M(跳转到默认页面)
    L --> N(解析IP归属地)
    N --> O(保存访问记录)
    O --> P(跳转到目标URL)
```

**流程说明**：
1. 外部用户点击短链接
2. 系统从URL中提取随机码部分
3. 系统校验随机码的有效性：
   - 有效：继续处理
   - 无效：跳转到默认页面（Google首页）
4. 系统记录访问信息（IP、设备等）
5. 系统解析访问者的IP归属地
6. 系统保存完整的访问记录
7. 系统将用户重定向至目标URL

## 3. 用户管理流程

以下流程图展示了系统中的用户管理流程：

```mermaid
flowchart TD
    A1(超级管理员登录系统) --> B1(进入管理员列表)
    B1 --> C1{选择操作类型}
    C1 -->|添加用户| D1(填写用户信息)
    C1 -->|修改用户| E1(修改用户信息)
    C1 -->|删除用户| F1(确认删除)
    D1 --> G1{选择角色}
    G1 -->|Boss| H1(创建Boss账号)
    G1 -->|经理| I1(选择上级Boss)
    I1 --> J1(创建经理账号)
    E1 --> K1(保存修改)
    F1 --> L1(执行删除)
    H1 --> M1(记录操作日志)
    J1 --> M1
    K1 --> M1
    L1 --> M1
```

**流程说明**：
1. 超级管理员登录系统并进入管理员列表
2. 超级管理员可以执行以下操作：
   - 添加用户：填写用户信息，选择角色（Boss或经理）
   - 修改用户：更新用户信息
   - 删除用户：删除现有用户
3. 创建Boss账号直接保存，创建经理账号需选择上级Boss
4. 所有管理操作都会记录到系统操作日志中

## 4. 多类型链接轮询流程

以下流程图展示了多类型链接轮询的配置和使用流程：

```mermaid
flowchart TD
    A2(用户配置轮询数据) --> B2(选择数据类型)
    B2 -->|WhatsApp| C2(输入WhatsApp账号)
    B2 -->|Telegram| D2(输入Telegram号码)
    B2 -->|网址| E2(输入URL地址)
    C2 --> F2(设置是否为新数据)
    D2 --> F2
    E2 --> F2
    F2 --> G2(保存轮询数据)
    
    H2(外部用户访问短链接) --> I2(系统提取随机码)
    I2 --> J2(获取下一个轮询数据)
    J2 --> K2{是否有新数据}
    K2 -->|有| L2(优先使用新数据)
    K2 -->|无| M2(按顺序轮询数据)
    L2 --> N2(拼接目标URL)
    M2 --> N2
    N2 --> O2(跳转到目标URL)
```

**流程说明**：
1. 用户配置轮询数据流程：
   - 用户选择数据类型（WhatsApp、Telegram或网址）
   - 输入对应类型的数据值
   - 设置是否为新数据（新数据优先级高）
   - 保存轮询数据到系统

2. 外部用户访问短链接流程：
   - 系统提取随机码并验证有效性
   - 系统获取下一个应该轮询的数据
   - 系统判断是否有新数据需要优先使用
   - 根据数据类型拼接目标URL
   - 将用户重定向到最终的目标URL

## 5. 域名管理流程

域名管理主要由超级管理员负责，流程包括：

1. **域名添加**：
   - 超级管理员在系统中添加新域名
   - 设置域名的有效期和初始状态（在用）
   - 系统保存域名信息，使其可在活码创建时使用

2. **域名有效期检查**：
   - 系统每月自动检查所有域名的有效期
   - 对已过期域名，自动将状态从"在用"更新为"不可用"
   - "不可用"域名在下级用户的选择列表中被隐藏

3. **域名删除**：
   - 超级管理员可删除未关联活码的域名
   - 删除操作需二次确认
   - 系统记录删除操作日志
