# 企业级活码管理系统功能文档

## 系统概述

本系统是一个基于React和Tailwind CSS构建的企业级活码管理系统，专注于短链接管理、多类型链接轮询跳转和访问统计分析。系统采用现代化的Web技术栈，提供完整的用户权限管理和数据可见性控制。

<!-- 
🔧 自定义系统概述区域
在此处添加您的特定业务描述和系统定位：
- 您的公司/组织特有的业务场景
- 系统在您业务中的具体作用
- 与其他系统的集成说明
-->

## 核心功能模块

### 1. 工作台 (Dashboard)
**功能描述：** 系统主控制面板，提供概览信息和快速操作入口

**主要特性：**
- 实时统计数据展示（短码总数、访问量、用户数量、域名状态）
- 最近活动日志展示
- 热门短码排行榜（支持权限控制显示创建者信息）
- 快速操作面板（创建活码、查看访问记录、用户管理等）
- 响应式设计，适配不同屏幕尺寸

**权限控制：**
- 超级管理员和Boss可查看所有数据
- 经理仅查看自己创建的数据

<!--
🔧 工作台自定义功能
在此处添加您希望在工作台展示的特定内容：
- 自定义统计指标
- 特殊的图表展示
- 业务相关的快捷操作
-->

### 2. 短码管理 (Short Code Management)
**功能描述：** 核心功能模块，负责短链接活码的创建、编辑和管理

**主要特性：**
- 支持三种类型：WhatsApp、Telegram、网址
- 自动生成随机短码，可手动刷新
- 活码状态自动判断（基于轮询数据）
- 分页展示和搜索功能
- 访问量和轮询数据快捷跳转
- 支持批量操作和导出

**创建流程：**
1. 填写活码标题（必填）
2. 选择类型（WhatsApp/Telegram/网址）
3. 系统自动生成随机码
4. 添加描述信息（可选）
5. 确认创建

**权限控制：**
- 所有角色可创建和管理自己的活码
- 超级管理员和Boss可查看下属活码
- 经理仅能查看和管理自己的活码

<!--
🔧 短码管理自定义功能
在此处添加您的特殊短码管理需求：
- 自定义短码生成规则
- 特殊的分类标签系统
- 批量导入/导出格式
- 其他类型的链接支持
-->

### 3. 链接轮询 (Link Polling)
**功能描述：** 多类型链接轮询跳转管理，支持批次优先级和轮询策略

**主要特性：**
- 支持批次管理和优先级设置
- 优先轮询机制（1-10次可配置）
- 批次状态自动转换（优先→常规）
- 链接使用统计和分析
- 批量添加和删除功能
- 分页展示和搜索过滤

**轮询逻辑：**
1. 优先级高的批次优先轮询
2. 达到优先次数后自动转为常规轮询
3. 同优先级内按添加时间排序
4. 支持手动调整和禁用

**批次管理：**
- 创建批次时设置优先轮询次数
- 支持批量导入链接数据
- 实时显示批次使用进度
- 完成状态可视化展示

<!--
🔧 链接轮询自定义策略
在此处添加您的特殊轮询需求：
- 自定义轮询算法
- 时间段控制策略
- 地域分配规则
- 负载均衡策略
-->

### 4. 访问记录 (Access Records)
**功能描述：** 详细的访问统计和分析功能

**主要特性：**
- 实时访问记录统计
- IP地理位置解析
- 设备类型识别（移动端/桌面端/平板）
- 来源渠道分析
- 时间范围筛选
- 分页展示和数据导出

**统计维度：**
- 总访问量和独立访客
- 设备分布统计
- 地理位置分布
- 时间趋势分析
- 来源渠道统计

<!--
🔧 访问记录自定义分析
在此处添加您需要的特殊统计维度：
- 自定义统计指标
- 特殊的报表格式
- 数据对接需求
- 实时监控需求
-->

### 5. 用户管理 (User Management)
**功能描述：** 三级权限体系的用户账户管理

**权限层级：**
- **超级管理员 (Super Admin)**：系统最高权限，可管理所有功能和用户
- **Boss**：可管理下属经理，查看所有业务数据
- **经理 (Manager)**：仅能管理自己的活码和数据

**管理功能：**
- 用户创建和编辑
- 角色权限分配
- 账户状态管理（启用/禁用）
- 密码重置
- 用户活码统计
- 分页展示和搜索

<!--
🔧 用户管理自定义权限
在此处添加您的特殊权限需求：
- 自定义角色定义
- 部门/组织架构集成
- 第三方认证集成
- 特殊权限控制规则
-->

### 6. 域名管理 (Domain Management)
**功能描述：** 系统域名配置和有效期监控

**主要特性：**
- 域名添加和配置
- 有效期监控和提醒
- 域名状态管理
- 关联活码统计
- 到期预警机制
- 域名地址创建后不可修改

**监控功能：**
- 5天内到期提醒
- 过期域名标识
- 关联活码影响评估
- 状态实时更新

<!--
🔧 域名管理自定义配置
在此处添加您的域名管理特殊需求：
- 域名自动续费集成
- DNS管理功能
- 证书管理功能
- CDN配置管理
-->

### 7. 操作日志 (Operation Logs)
**功能描述：** 完整的系统操作审计和日志记录

**记录内容：**
- 用户操作行为
- 操作时间和IP地址
- 操作目标和详细描述
- 操作结果状态
- 用户角色信息

**日志类型：**
- 用户登录/登出
- 活码创建/编辑/删除
- 用户管理操作
- 系统配置变更
- 数据导入/导出
- 权限变更

**查询功能：**
- 多维度筛选（时间、用户、操作类型）
- 关键词搜索
- 状态过滤
- 分页展示
- 日志导出

<!--
🔧 操作日志自定义需求
在此处添加您的特殊日志需求：
- 自定义日志字段
- 第三方日志系统集成
- 合规性要求配置
- 长期存储策略
-->

## 技术特性

### 前端技术栈
- **React 18**：现代化前端框架
- **TypeScript**：类型安全的JavaScript
- **Tailwind CSS v4**：原子化CSS框架
- **Shadcn/ui**：高质量组件库
- **Lucide React**：现代化图标库

### 响应式设计
- 移动优先的响应式设计
- 支持多种屏幕尺寸
- 触控友好的交互设计
- 自适应布局和组件

### 性能优化
- 分页加载减少数据传输
- 组件懒加载
- 状态管理优化
- 缓存策略

### 安全特性
- 三级权限控制
- 数据可见性隔离
- 操作审计日志
- 输入验证和过滤

<!--
🔧 技术栈自定义扩展
在此处添加您计划使用的额外技术：
- 第三方库集成
- 特殊组件需求
- 性能监控工具
- 安全防护措施
-->

## 权限矩阵

| 功能模块 | 超级管理员 | Boss | 经理 |
|---------|-----------|------|------|
| 工作台 | ✅ 全部数据 | ✅ 全部数据 | ✅ 个人数据 |
| 短码管理 | ✅ 全部活码 | ✅ 全部活码 | ✅ 个人活码 |
| 链接轮询 | ✅ 全部配置 | ✅ 全部配置 | ✅ 个人配置 |
| 访问记录 | ✅ 全部记录 | ✅ 全部记录 | ✅ 个人记录 |
| 用户管理 | ✅ 全部用户 | ✅ 下属用户 | ❌ 无权限 |
| 域名管理 | ✅ 全部域名 | ❌ 无权限 | ❌ 无权限 |
| 操作日志 | ✅ 全部日志 | ✅ 相关日志 | ❌ 无权限 |

<!--
🔧 权限矩阵自定义
在此处添加您的特殊权限配置：
- 自定义角色权限
- 特殊功能模块权限
- 数据范围控制规则
- 临时权限机制
-->

## 数据流程

### 活码创建流程
1. 用户创建活码并设置基本信息
2. 系统生成唯一短码
3. 在链接轮询模块中添加目标链接
4. 配置轮询策略和优先级
5. 活码激活，开始接收访问

### 访问跳转流程
1. 用户访问短链接
2. 系统记录访问信息（IP、设备、时间等）
3. 根据轮询策略选择目标链接
4. 执行跳转并更新统计数据
5. 记录操作日志

### 权限验证流程
1. 用户登录系统
2. 获取用户角色和权限
3. 前端路由权限验证
4. 数据访问权限过滤
5. 操作权限实时检查

<!--
🔧 数据流程自定义
在此处添加您的特殊业务流程：
- 自定义审批流程
- 数据同步机制
- 第三方系统对接流程
- 特殊业务规则
-->

## 部署和维护

### 环境要求
- Node.js 18+
- 现代化浏览器支持
- 响应式设计适配

### 扩展性
- 模块化架构设计
- 组件可复用
- 配置可外部化
- 支持多语言扩展

### 监控和日志
- 完整的操作审计
- 性能监控指标
- 错误日志收集
- 用户行为分析

<!--
🔧 部署维护自定义
在此处添加您的部署和维护特殊需求：
- 特定的部署环境要求
- 自定义监控指标
- 备份恢复策略
- 升级维护计划
-->

## 系统优势

1. **企业级安全**：三级权限体系，完整的审计日志
2. **高可用性**：防封域名轮换，多重备份机制
3. **数据洞察**：详细的访问统计和用户行为分析
4. **操作便捷**：直观的用户界面，快捷的操作流程
5. **可扩展性**：模块化设计，支持功能扩展
6. **响应式**：完美适配各种设备和屏幕尺寸

<!--
🔧 系统优势自定义
在此处添加您的系统特有优势：
- 行业特定的优势
- 竞争对手比较
- 成本效益分析
- ROI评估
-->

## 更新日志

### v1.0.0 (2024-01)
- 完成核心功能开发
- 实现三级权限体系
- 添加分页和搜索功能
- 完善操作日志系统
- 优化用户体验和界面设计

### 后续规划
- API集成和后端对接
- 更多统计分析功能
- 批量操作功能增强
- 移动端App开发
- 第三方平台集成

<!--
🔧 更新规划自定义
在此处添加您的特定更新计划：
- 业务功能扩展计划
- 技术架构升级计划
- 第三方集成计划
- 性能优化计划
-->

---

**📝 使用说明：** 本文档中标注 "🔧 自定义" 的区域是为您预留的，可以根据具体业务需求添加相应的内容。请保持文档的整体结构和格式一致。