# 活码系统代码规范

## 前端代码规范

### 文件与目录结构

```
src/
├── assets/            # 静态资源
├── components/        # 共用组件
│   ├── ui/            # 基于Radix UI和Shadcn UI的基础组件
│   ├── layout/        # 布局组件
│   └── business/      # 业务组件
├── composables/       # 组合式API
├── router/            # 路由配置
├── stores/            # Pinia状态管理
├── styles/            # 全局样式
├── types/             # TypeScript类型定义
├── utils/             # 工具函数
└── views/             # 页面组件
    ├── admin/         # 超级管理员页面
    ├── boss/          # Boss页面
    └── manager/       # 经理页面
```

### 命名规范

1. **组件命名**：使用 PascalCase
   - 基础组件以特定前缀命名，如 `Base`, `App`, `V`
   - 单例组件以 `The` 前缀命名
   - 紧密耦合的组件名称添加父组件名作为前缀

2. **文件命名**：
   - 组件文件使用 PascalCase，如 `ActivityManager.vue`
   - 非组件文件使用 kebab-case，如 `auth-service.js`

3. **变量命名**：
   - 常量使用 UPPER_SNAKE_CASE
   - 变量使用 camelCase
   - 私有变量以下划线开头，如 `_privateVar`

4. **CSS类命名**：使用 kebab-case
   - 避免使用单个字母和语义不明确的缩写
   - 使用 BEM 命名约定：`block__element--modifier`

### UI组件使用规范

1. **Radix UI 原语使用规范**
   - 直接导入所需原语，避免全局导入
   - 优先使用原语的无障碍功能，不要覆盖默认的键盘交互
   - 原语组件命名遵循官方规范，如 Dialog, DropdownMenu
   
   **使用说明**：
   - 对话框组件应保持完整的组件结构，包括根元素、触发器、内容、标题、描述和关闭按钮
   - 确保键盘导航和焦点管理，如按Esc键关闭对话框，Tab键在对话框内循环焦点
   - 保留ARIA属性以确保屏幕阅读器可以正确识别组件角色和状态
   - 使用Portal确保模态内容正确渲染在DOM层级中

2. **Shadcn UI 组件使用规范**
   - 保持组件API的一致性，避免不必要的自定义
   - 使用Shadcn UI提供的变体系统进行样式扩展
   - 遵循组件的使用模式，特别是复合组件的结构
   
   **使用说明**：
   - 表格组件应包含表头、表体、单元格等完整结构
   - 按钮组件应使用预定义的变体（primary、secondary、outline等）
   - 表单组件应保持一致的验证和错误提示方式
   - 复合组件（如下拉菜单）应保持子组件的正确嵌套顺序

3. **组件定制**
   - 优先使用CSS变量进行定制，而不是覆盖组件内部样式
   - 为自定义组件创建明确的变体定义
   - 保持定制与原始设计语言的一致性
   
   **定制方法**：
   - 在根级别定义CSS变量，组件内部引用这些变量
   - 通过修改CSS变量值来改变组件外观，而不是直接修改组件样式
   - 为不同用户角色创建主题变体，通过类名切换应用不同主题

4. **组件文档**
   - 为每个自定义组件编写使用文档
   - 记录组件的属性、事件和插槽
   - 提供使用示例
   
   **文档格式**：
   - 组件描述：简要说明组件的用途和适用场景
   - 属性列表：列出所有属性，包括类型、默认值和说明
   - 事件列表：列出组件触发的所有事件及其参数
   - 插槽说明：描述组件提供的插槽及其用途
   - 使用示例：提供基本用法和常见场景的文字描述

### 基于角色的UI主题规范

1. **主题结构**
   - 基础主题变量定义在全局CSS文件中
   - 角色特定主题继承基础主题并覆盖差异化变量
   - 通过在根元素添加角色类名应用主题，如 admin-theme, boss-theme, manager-theme

2. **超级管理员主题 (admin-theme)**
   - 主色调：深紫色 (#5e35b1) 和亮紫色 (#9c27b0) 渐变
   - 强调色：亮紫色 (#9c27b0)
   - 背景色：更深的背景 (#f5f5f5) 体现专业感
   - 更多高级操作按钮
   - 完整的数据表格列

3. **Boss主题 (boss-theme)**
   - 主色调：深蓝色 (#1976d2) 和天蓝色 (#03a9f4) 渐变
   - 强调色：蓝色 (#1976d2)
   - 背景色：标准白色背景
   - 团队管理相关组件样式突出
   - 数据可视化组件强调

4. **经理主题 (manager-theme)**
   - 主色调：绿色 (#43a047) 和浅绿色 (#8bc34a) 渐变
   - 强调色：绿色 (#43a047)
   - 背景色：更明亮的背景 (#ffffff)
   - 简化的界面元素
   - 聚焦于个人数据的组件样式

5. **主题切换**
   - 基于用户角色计算主题类名
   - 在应用根元素上应用对应的主题类
   - 组件通过CSS变量继承主题样式

### Vue 3 代码规范

1. **Composition API 使用规则**
   - 使用 `<script setup>` 语法
   - 使用 ref, reactive, computed, watch 等响应式API
   - 按功能组织代码，相关逻辑放在一起

2. **Props 定义**
   - 使用 TypeScript 类型进行属性定义
   - 提供默认值和验证规则
   - 命名使用 camelCase

3. **事件处理**
   - 使用 emit 定义组件事件
   - 事件处理函数命名以 handle 或 on 开头
   - 事件名使用 kebab-case

4. **生命周期钩子**
   - 使用 Composition API 的生命周期钩子
   - 在组件顶部统一导入所需的生命周期函数

### 性能优化规范

1. **组件优化**
   - 使用 v-memo 减少不必要的重渲染
   - 使用 shallowRef 和 shallowReactive 优化大型对象
   - 使用 defineAsyncComponent 进行组件懒加载

2. **渲染优化**
   - 使用 v-once 渲染静态内容
   - 使用 v-for 时提供 key
   - 使用 computed 缓存计算结果

3. **打包优化**
   - 使用 Radix UI 和 Shadcn UI 的按需导入
   - 拆分大型组件到独立的 chunk
   - 利用浏览器缓存策略

### 代码注释规范

1. **组件注释**
   - 组件顶部添加描述、作者、创建日期
   - 复杂逻辑添加详细注释
   - 使用 JSDoc 格式为函数添加注释

2. **TODO注释**
   - 使用统一格式 `// TODO: 内容`
   - 添加处理人和优先级 `// TODO(开发者): 高优先级 - 内容`

## 后端代码规范

### Java代码规范

1. **命名规范**
   - 类名使用 PascalCase
   - 方法名和变量使用 camelCase
   - 常量使用 UPPER_SNAKE_CASE
   - 包名全部小写

2. **类设计规范**
   - 遵循单一职责原则
   - 控制类的大小，避免超大类
   - 使用接口分离原则

3. **异常处理**
   - 使用具体的异常类型，而不是通用异常
   - 记录异常信息到日志
   - 合理使用检查型异常和非检查型异常

### API设计规范

1. **RESTful API设计**
   - 使用名词表示资源
   - 使用HTTP方法表示操作
   - 使用HTTP状态码表示结果

2. **请求与响应格式**
   - 使用统一的请求和响应格式
   - 使用清晰的错误码和错误信息
   - 支持分页、排序和过滤

## 通用规范

### 代码质量工具

1. **Linting**
   - 前端：ESLint + Prettier
   - 后端：Checkstyle + PMD

2. **类型检查**
   - 前端：TypeScript 严格模式
   - 后端：强制使用泛型

3. **测试**
   - 单元测试覆盖核心逻辑
   - 组件测试覆盖关键组件
   - 端到端测试覆盖关键流程

### Git提交规范

1. **提交消息格式**
```
<type>(<scope>): <subject>

<body>

<footer>
```

2. **类型（type）**
   - feat: 新功能
   - fix: 修复bug
   - docs: 文档更新
   - style: 代码风格修改
   - refactor: 代码重构
   - perf: 性能优化
   - test: 测试相关
   - build: 构建相关
   - ci: CI相关

3. **分支管理**
   - master/main: 主分支
   - develop: 开发分支
   - feature/*: 功能分支
   - bugfix/*: 缺陷修复分支
   - release/*: 发布分支

### 代码审查

1. **审查重点**
   - 功能正确性
   - 代码质量和可维护性
   - 性能考量
   - 安全风险

2. **审查流程**
   - 创建拉取请求
   - 至少一名团队成员审查
   - 自动化测试通过
   - 解决所有评论后合并

### 文档规范

1. **代码文档**
   - 前端：使用 JSDoc
   - 后端：使用 Javadoc

2. **项目文档**
   - README.md 包含项目概述和启动指南
   - CONTRIBUTING.md 包含贡献指南
   - 架构文档描述系统设计

## Radix UI 和 Shadcn UI 最佳实践

### 组件封装原则

1. **保持原始功能完整性**
   - 封装 Radix UI 原语时保留所有可访问性特性
   - 不覆盖默认的键盘交互行为
   - 确保组件状态管理的一致性

2. **统一视觉风格**
   - 使用 Shadcn UI 的设计语言确保组件视觉一致
   - 基于角色的主题变量应用于所有组件
   - 维持组件间的视觉层次和间距关系

3. **组件复合设计**
   - 遵循组件复合模式，保持子组件的正确嵌套关系
   - 确保组件间的通信遵循单向数据流
   - 使用插槽和作用域插槽提供灵活的内容定制能力

### 角色特定组件变体

1. **变体定义方法**
   - 通过 CSS 变量和类名组合定义组件变体
   - 为每个角色创建专属的变体样式
   - 保持变体间的一致性，仅在必要的视觉元素上有差异

2. **变体应用策略**
   - 基于用户角色自动应用对应的组件变体
   - 组件接收角色属性，内部处理样式逻辑
   - 避免在组件外部直接操作组件样式

3. **变体切换机制**
   - 角色变更时，通过状态管理触发全局主题切换
   - 组件响应主题变更，应用对应的样式变量
   - 确保切换过程平滑，可添加过渡效果 