# 活码系统 - 技术栈规范

## 一、前端技术栈

### 核心框架
- Vue 3.x
- 使用 Composition API
- TypeScript 支持
- 严格模式确保类型安全

### UI组件
- **Radix UI** - 无样式的低级UI原语
  - 用于构建可访问性强的交互组件
  - 主要用于复杂交互组件，如下拉菜单、对话框、弹出提示等
  - 版本要求：最新稳定版
  - 适用于活码系统的高交互区域，如表单弹窗、下拉选项等

- **Shadcn UI** - 基于Radix UI构建的高质量组件库
  - 用于快速开发具有一致设计语言的界面
  - 提供了预设样式但保持高度可定制性
  - 应用于表格、表单、按钮等常用界面元素
  - 版本要求：最新稳定版
  - 适合活码管理、用户管理等主要功能模块的快速开发

### CSS方案
- TailwindCSS
- CSS变量用于主题定制
- 遵循组件库推荐的样式扩展方式
- 自定义工具类适配活码系统特定需求

### 状态管理
- Pinia (Vue 3官方推荐)
- 按照模块划分store
  - userStore: 用户信息与权限
  - codeStore: 活码数据管理
  - domainStore: 域名管理
  - settingsStore: 系统设置

### 路由管理
- Vue Router
- 基于角色的路由守卫
- 路由元信息存储权限配置

### 网络请求
- Axios
- 封装统一的请求拦截与响应处理
- 请求取消机制，避免重复请求
- 基于角色的API权限验证

### 工具库
- dayjs (日期处理)
- lodash-es (工具函数)
- uuid (唯一标识生成)
- clipboard-js (剪贴板操作，用于复制活码链接)

### 构建工具
- Vite
- 环境变量配置区分开发/测试/生产环境
- 打包优化策略，减小体积

## 后端技术栈

### 语言与框架
- Java
- Spring Boot
- Spring MVC 处理Web请求

### 数据库
- MySQL
- Redis (缓存)
  - 短链接跳转规则缓存
  - 热门活码数据缓存
  - 用户会话管理

### API规范
- RESTful API设计
- 统一返回格式
- Swagger文档
- 接口版本控制策略

## UI设计规范

### 设计系统
- 基于Shadcn UI组件，构建一致的设计语言
- 三种用户角色（超级管理员、Boss、经理）使用统一的设计系统，通过权限控制显示不同功能
- 每个角色定制专属的视觉主题，保持品牌一致性

### 颜色系统
- 主色调：根据用户角色区分
  - 超级管理员：深紫色 (#5e35b1) 与亮紫色 (#9c27b0) 渐变
  - Boss：深蓝色 (#1976d2) 与天蓝色 (#03a9f4) 渐变
  - 经理：绿色 (#43a047) 与浅绿色 (#8bc34a) 渐变
- 中性色：用于文本、背景、边框等
  - 主文本: #1c2536
  - 次要文本: #65748b
  - 边框色: #e5e7eb
  - 背景色: #f9fafc
- 功能色：成功、警告、错误、信息提示等
  - 成功: #4caf50
  - 警告: #ff9800
  - 错误: #f44336
  - 信息: #2196f3

### 组件使用规范

#### 导航与布局
- 使用Radix UI的NavigationMenu构建主导航
- 响应式布局采用Sheet组件实现侧边栏折叠
- 基于角色的动态导航菜单，自动隐藏无权限项

#### 表格与数据展示
- 使用Shadcn UI的Table组件，确保数据表格的一致性和可访问性
- 分页控件采用Shadcn UI的Pagination组件
- 数据过滤与排序使用DataTableViewOptions组件
- 空数据状态显示友好提示

#### 表单与输入
- 表单验证统一使用Shadcn UI的Form组件
- 下拉选择使用Select或Combobox组件
- 日期选择使用DatePicker组件
- 活码表单中的特殊字段使用自定义输入组件

#### 对话框与提示
- 模态对话框使用Dialog组件
- 侧边抽屉使用Drawer组件
- 轻提示使用Toast组件
- 操作确认使用AlertDialog组件

#### 按钮与操作项
- 按钮统一使用Shadcn UI的Button组件
- 下拉操作菜单使用DropdownMenu组件
- 批量操作使用CommandMenu组件
- 活码操作按钮根据角色权限动态显示

### 响应式设计
- 移动优先设计思想
- 断点设置：sm(640px), md(768px), lg(1024px), xl(1280px), 2xl(1536px)
- 不同设备下的导航栏自适应策略
- 表格在小屏幕下的展示方案

## 开发规范

### 组件开发
- 遵循单一职责原则
- 组件命名采用PascalCase
- Props定义明确类型和默认值
- 活码相关组件独立封装，提高复用性

### 代码风格
- 使用ESLint和Prettier保证代码质量和一致性
- 遵循项目定义的代码规范
- 文档注释完善，便于团队协作

### 提交规范
- 采用Conventional Commits规范
- 提交前进行代码检查和测试
- 重要功能添加单元测试覆盖

## 性能优化

- 组件懒加载
- 合理使用缓存
- 图片优化
- 避免不必要的渲染
- 活码表格数据虚拟滚动
- 大数据集分页加载策略

## 安全规范

- 输入验证
- XSS防护
- CSRF防护
- 敏感数据加密
- 短链接访问频率限制
- 防暴力破解机制

## 基于角色的主题系统

### 主题变量设计
- 基础变量定义：定义所有通用的颜色、间距、字体等变量
- 角色特定变量：针对不同角色覆盖基础变量中的特定值
- 组件变量映射：将基础变量映射到具体组件的CSS变量

### 主题应用策略
- 通过数据驱动的方式根据用户角色应用对应主题类
- 在应用根元素上添加角色特定的主题类
- 组件通过CSS变量继承主题样式

### 主题切换机制
- 登录时自动根据用户角色应用相应主题
- 提供手动切换明暗模式的功能
- 主题偏好保存在本地存储中，下次登录自动应用 