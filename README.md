# 企业级活码管理系统文档

## 项目概述

企业级活码管理系统是一个基于Vue 3和Spring Boot的现代化短链接管理平台，支持多级用户权限管理、链接轮询跳转、访问记录统计等功能。

## 技术栈

### 前端
- **框架**: Vue 3 (Composition API + `<script setup>`)
- **类型系统**: TypeScript
- **构建工具**: Vite
- **UI组件库**: Radix UI + Shadcn UI
- **状态管理**: Pinia
- **路由**: Vue Router

### 后端
- **框架**: Spring Boot
- **数据库**: MySQL 8.0+
- **缓存**: Redis
- **安全**: Spring Security + JWT
- **IP解析**: MaxMind GeoLite2

## 文档结构

### 📋 需求与设计
- [活码系统设计需求](doc/需求与设计/活码系统设计需求.md) - 系统核心功能需求
- [业务流程](doc/需求与设计/业务流程.md) - 详细业务流程说明
- [流程图](doc/需求与设计/流程图.md) - 可视化业务流程

### 🏗️ 数据库与架构
- [项目架构](doc/数据库与架构/项目架构.md) - 完整的技术架构设计
- [数据库设计](database/README.md) - 数据库部署和维护说明
- [表结构定义](database/schema.sql) - 完整的数据库表结构
- [实体关系图](database/er_diagram.md) - 数据库ER图和关系说明

### 🔐 权限管理
- [权限表格](doc/权限表格.md) - 详细的权限矩阵定义
- [功能文档](doc/功能文档.md) - 系统功能和权限说明

### 🤝 前后端协作
- [接口文档](doc/前后端协作/接口文档.md) - 完整的API接口规范
- [模块职责划分](doc/前后端协作/模块前后端职责划分.md) - 前后端开发职责

### 📐 开发规范
- [代码规范](doc/开发规范/代码规范.md) - 前后端代码规范
- [技术栈规范](doc/开发规范/技术栈规范.md) - 技术选型和使用规范
- [测试规范](doc/开发规范/测试规范.md) - 测试标准和流程
- [部署规范](doc/开发规范/部署规范.md) - 部署和运维规范

### 🎨 界面设计
- [网站界面风格指南](doc/界面设计/网站界面风格指南.md) - UI设计规范和风格指南

### 📊 项目管理
- [任务清单](doc/项目管理/任务清单.md) - 开发任务和进度管理
- [开发步骤与进度计划](doc/项目管理/开发步骤与进度计划.md) - 项目开发计划

### 🚀 部署指南
- [部署上线指南](doc/部署上线指南.md) - 完整的部署和上线流程

### 📁 归档文件
- [历史决策记录](doc/归档/) - 重要的技术决策和问题解答记录

## 快速开始

### 环境要求
- Node.js 18+
- MySQL 8.0+
- Redis 6.0+
- Java 17+

### 开发环境搭建
1. 克隆项目代码
2. 安装前端依赖：`npm install`
3. 配置数据库连接
4. 启动后端服务
5. 启动前端开发服务器：`npm run dev`

详细的部署说明请参考 [部署上线指南](doc/部署上线指南.md)。

## 核心功能

### 用户权限管理
- **三级权限体系**：超级管理员、Boss、经理
- **数据权限控制**：基于角色的数据可见性
- **操作权限管理**：细粒度的功能权限控制

### 活码管理
- **活码创建**：支持自定义标题和域名选择
- **随机码生成**：系统自动生成唯一随机码
- **批量管理**：支持批量操作和管理

### 链接轮询
- **多类型支持**：WhatsApp、Telegram、普通网址
- **优先级控制**：支持批次优先级和轮询策略
- **智能跳转**：基于轮询算法的智能跳转

### 访问统计
- **实时记录**：详细的访问记录和统计
- **IP归属地**：自动解析访问者地理位置
- **数据导出**：支持数据导出和分析

## 贡献指南

1. 阅读 [代码规范](doc/开发规范/代码规范.md)
2. 查看 [开发变更记录](doc/开发规范/开发变更记录文档.md)
3. 遵循 Git 提交规范
4. 提交 Pull Request

## 许可证

本项目采用 MIT 许可证。

## 联系方式

如有问题或建议，请通过以下方式联系：
- 项目Issues
- 技术文档反馈

---

**文档版本**: v1.0  
**最后更新**: 2025-01-31  
**维护者**: 开发团队
