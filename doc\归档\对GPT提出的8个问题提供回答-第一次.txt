相应问题的处理操作，并在原文档中涉及的部分进行更改。
1. 域名过期与活码可用性的矛盾
    处理办法：域名 DNS 到了过期时间之后，相应的已关联过期域名活码后面标出“已过期域名的活码”特殊标识。
2. 随机码唯一性与不可编辑性的矛盾
    处理办法：随机码生成与校验，由后端把关。后端直接生成并返回。
3. 权限分配的粒度与实际操作范围的矛盾
    处理办法：超级管理员配置为最高优先，Boss仅能在授权范围内调整，且不能覆盖超级管理员的限制。权限变更应有日志记录，便于追溯。
4. 管理员账号删除与业务数据关联的矛盾
    处理办法：采用“禁用”状态替代删除，保留历史数据归属，防止数据丢失。“禁用”的Boss或者经理将移入折叠列表。
5. 访问记录批量删除与权限控制的矛盾
    处理办法：“全部删除”操作的作用范围，批量删除只是在前端界面不再显示，但是后台记录的信息并不会实际上删除。按照这样的逻辑进行设计，所有等级的用户都是如此。
6. 角色字段不可编辑与角色变更需求的矛盾
    处理办法：普通用户不可编辑角色，超级管理员可通过专用操作变更角色，并记录日志。
7. 过期域名删除与活码历史数据的矛盾
    处理办法：域名过期删除模块就给它取消掉，域名管理模块添加域名状态，超级管理员可对状态进行更改，如果改成禁用状态，前端展示页面就不再显示该域名。
8. 访问记录IP归属地解析的实现细节
    处理办法：使用MaxMind GeoLite2 DB来解析 IP 地理位置，并帮我生成一个系统设计需求 - IP归属地解析模块，现在只是需求文档，代码不用给我，描述清楚操作流程就好。
