# 短链接系统 - 测试规范

## 一、通用测试原则

1. **测试先行原则**
   - 采用测试驱动开发(TDD)或行为驱动开发(BDD)方法
   - 编写功能代码前先编写测试用例
   - 确保测试覆盖需求的核心功能和边界条件

2. **测试隔离原则**
   - 每个测试应独立运行，不依赖其他测试的结果
   - 使用mock、stub等技术隔离外部依赖
   - 确保测试环境的一致性和可重复性

3. **测试覆盖率要求**
   - 整体代码覆盖率目标：70%以上
   - 核心业务逻辑覆盖率：85%以上
   - 关键功能模块覆盖率：90%以上

4. **测试命名约定**
   - 测试类命名：`[被测试类名]Test`
   - 测试方法命名：`test[方法名]_[测试场景]`
   - 测试描述应清晰表达测试目的和预期结果

5. **测试生命周期管理**
   - 测试用例与代码同步更新
   - 对于废弃的功能，同步移除相关测试
   - 定期执行全量测试确保系统稳定性

## 二、前端测试规范

### 1. 单元测试
- **框架选择**：使用Vitest作为单元测试框架
- **测试范围**：
  - 工具函数
  - Composables（组合式函数）
  - Pinia store
  - 独立的业务逻辑
- **测试文件位置**：与被测试文件放在同一目录下，命名为`*.spec.ts`
- **基本结构**：
  ```typescript
  import { describe, it, expect } from 'vitest'
  
  describe('模块名/函数名', () => {
    it('应该能正确处理xx情况', () => {
      // Arrange - 准备测试数据
      // Act - 执行被测试代码
      // Assert - 断言结果
    })
  })
  ```
- **覆盖率要求**：70%以上

### 2. 组件测试
- **框架选择**：Vue Test Utils + Testing Library
- **测试范围**：
  - UI组件渲染
  - 组件交互行为
  - 组件props和事件
  - 组件生命周期
- **测试文件位置**：与组件放在同一目录下，命名为`*.spec.ts`
- **基本结构**：
  ```typescript
  import { describe, it, expect } from 'vitest'
  import { mount } from '@vue/test-utils'
  import { render, screen, fireEvent } from '@testing-library/vue'
  import MyComponent from './MyComponent.vue'
  
  describe('MyComponent', () => {
    it('正确渲染初始状态', () => {
      // 测试组件初始渲染
    })
    
    it('点击按钮应触发预期行为', async () => {
      // 测试交互行为
    })
  })
  ```
- **覆盖率要求**：60%以上

### 3. 端到端测试
- **框架选择**：Cypress
- **测试范围**：
  - 用户关键流程
  - 表单提交
  - 页面导航
  - API集成
- **测试文件位置**：`/cypress/e2e/*.cy.ts`
- **基本结构**：
  ```typescript
  describe('活码管理', () => {
    beforeEach(() => {
      cy.login() // 自定义命令处理登录
      cy.visit('/qrcodes')
    })
    
    it('应能成功创建新活码', () => {
      // 测试创建活码流程
    })
  })
  ```
- **覆盖率要求**：
  - 关键业务流程100%覆盖
  - 常见错误处理场景覆盖

### 4. 前端测试最佳实践
- 优先测试业务逻辑，而非DOM结构
- 使用`data-testid`属性而非CSS选择器定位元素
- 模拟API请求，避免真实网络调用
- 测试组件时关注其行为而非实现细节
- 对于复杂表单，测试验证逻辑和提交行为

## 三、后端测试规范

### 1. 单元测试
- **框架选择**：JUnit 5
- **测试范围**：
  - Service层业务逻辑
  - Repository层数据访问
  - Util工具类
  - 自定义算法
- **测试文件位置**：`src/test/java`目录下，包结构与主代码相同
- **基本结构**：
  ```java
  @ExtendWith(MockitoExtension.class)
  class UserServiceTest {
      @Mock
      private UserRepository userRepository;
      
      @InjectMocks
      private UserServiceImpl userService;
      
      @Test
      void testCreateUser_WithValidData_ShouldReturnCreatedUser() {
          // Arrange
          UserCreateDTO dto = new UserCreateDTO();
          // 设置测试数据...
          
          // Act
          UserInfo result = userService.createUser(dto);
          
          // Assert
          assertNotNull(result);
          assertEquals(dto.getUsername(), result.getUsername());
      }
  }
  ```
- **覆盖率要求**：80%以上

### 2. 集成测试
- **框架选择**：Spring Boot Test
- **测试范围**：
  - Controller层接口
  - 多个组件的协作
  - 数据库交互
- **测试文件位置**：`src/test/java`目录下，以`IT`结尾命名
- **基本结构**：
  ```java
  @SpringBootTest
  @AutoConfigureMockMvc
  class UserControllerIT {
      @Autowired
      private MockMvc mockMvc;
      
      @Autowired
      private ObjectMapper objectMapper;
      
      @Test
      void testCreateUser_WithValidData_ShouldReturn200AndCreatedUser() throws Exception {
          // 准备测试数据
          UserCreateDTO dto = new UserCreateDTO();
          // 设置数据...
          
          // 执行请求并验证结果
          mockMvc.perform(post("/api/users")
                  .contentType(MediaType.APPLICATION_JSON)
                  .content(objectMapper.writeValueAsString(dto)))
                  .andExpect(status().isOk())
                  .andExpect(jsonPath("$.code").value(200))
                  .andExpect(jsonPath("$.data.username").value(dto.getUsername()));
      }
  }
  ```
- **覆盖率要求**：70%以上

### 3. 数据库测试
- **框架选择**：TestContainers或H2内存数据库
- **测试范围**：
  - Repository层查询方法
  - 数据库事务
  - JPA映射
- **测试文件位置**：`src/test/java`目录下，以`RepositoryTest`结尾命名
- **基本结构**：
  ```java
  @DataJpaTest
  class UserRepositoryTest {
      @Autowired
      private UserRepository userRepository;
      
      @Test
      void testFindByUsername_ShouldReturnUser() {
          // 准备测试数据
          User user = new User();
          user.setUsername("testuser");
          userRepository.save(user);
          
          // 执行查询
          Optional<User> result = userRepository.findByUsername("testuser");
          
          // 验证结果
          assertTrue(result.isPresent());
          assertEquals("testuser", result.get().getUsername());
      }
  }
  ```
- **覆盖率要求**：75%以上

### 4. 后端测试最佳实践
- 使用测试夹具（Test Fixtures）准备测试数据
- 对于外部依赖，使用Mock对象或测试替身
- 测试异常场景和边界条件
- 使用断言库表达清晰的测试预期
- 保持测试的独立性和可重复性

## 四、测试自动化与CI/CD集成

1. **自动化测试流程**
   - 在开发环境中执行单元测试和组件测试
   - 在CI环境中执行全套测试（单元、集成、端到端）
   - 代码合并前必须通过全部测试

2. **CI/CD集成**
   - 使用GitHub Actions或Jenkins配置自动化测试
   - 在每次Pull Request时触发测试
   - 生成测试覆盖率报告
   - 设置质量门禁，不满足要求的代码不允许合并

3. **测试报告**
   - 生成HTML格式的测试报告
   - 记录测试覆盖率数据
   - 提供测试失败的详细信息
   - 保存测试历史记录，便于分析趋势

4. **性能测试与压力测试**
   - 使用JMeter或K6进行API性能测试
   - 定期执行压力测试，确保系统在高负载下的稳定性
   - 设置性能基准，防止性能退化 