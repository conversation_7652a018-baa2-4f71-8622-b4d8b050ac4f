# 活码系统

截至目前，已明确的系统设计需求模块共 **9 个**，额外模块**3个**，各模块聚焦不同核心功能，且存在逻辑关联，形成系统基础框架：

1. **短码管理模块**  
   - 核心：短码的创建、查询、修改、删除等全生命周期管理，含数据交互、权限控制、分页筛选功能。

2. **活码添加模块**  
   - 核心：活码添加弹窗的表单交互（标题、域名选择、随机码生成），及随机码与 URL 的关联规则。

3. **访问记录模块**  
   - 核心：活码访问记录的展示（编号、活码 ID、时间、IP 等）、关联跳转（从访问数入口进入）、批量删除、分页功能。

4. **短链接跳转规则模块**  
   - 核心：明确系统生成的有效随机码跳转至指定链接，无效随机码/无随机码跳转至 Google 首页的规则。

5. **权限与数据管理模块**  
   - 核心：超级管理员、Boss、经理的角色定义、权限分配（细化到按钮级）、数据可见范围（超级管理员看全部、Boss看下属、经理看自己）。

6. **管理员列表及用户管理模块**  
   - 核心：管理员列表的字段展示（编号、昵称、账号等）、添加用户（字段规则与角色权限限制）、修改管理员信息（可编辑/不可编辑字段区分）。

7. **域名管理模块**
   - 核心：管理系统内购买的前端域名，聚焦域名的有效期管控、状态区分及过期处理。

8. **IP 归属地解析模块**
   - 核心：解析访问记录中的IP地址，获取其地理位置信息，提升数据分析的准确性和可视化能力。

9. **多类型链接轮询跳转模块**
   - 核心：管理和轮询多种类型的链接数据（包括WhatsApp、Telegram、网址等），每个活码关联自己的专属链接数据集合，确保每次活码链接访问时，根据预设的轮询规则（新批次优先），将对应数据动态拼接到相应URL后进行跳转，以实现流量的均衡分配和新业务的优先处理。

后续可基于这些模块，继续补充扩展模块，完善系统功能闭环。

---

## 系统设计需求 - 活码添加模块

### 一、概述
本模块实现活码添加功能，需支持**活码标题、前端域名、随机码（动态生成）、备注录入**，保障随机码生成规则及与 URL 关联跳转逻辑准确，为活码管理提供基础创建能力。 

### 二、功能需求
#### （一）表单基础交互
1. **字段录入**：
    - 活码标题：用户可输入文本内容，作为活码标识，为必填项，需校验非空（如输入为空时提示"请输入活码标题"）。 
    - 前端域名：通过下拉框选择预设的前端域名，为必填项，无合法选项时禁用"确定"按钮，提示"请选择前端域名"。 
    - 随机码：每次打开"添加活码"弹窗时，系统自动生成随机字符串，生成规则如下：
      - **字符集**：使用大小写字母和数字组合（`ABCDEFGHJKLMNOPQRSTUVWXYZabcdefghijkmnopqrstuvwxyz1234567890`），排除易混淆字符（I、l、O、0等），确保用户识别准确性
      - **长度规范**：默认8位字符，可通过系统配置调整（支持6-12位范围）
      - **生成算法**：后端使用安全随机数生成器，确保随机性和不可预测性
      - **唯一性保障**：生成后立即校验数据库唯一性，如重复则重新生成（最多重试3次）
      - **显示特性**：用户不可编辑，作为活码关键标识，支持一键复制功能
    - 备注：选填项，用户可输入补充说明文本，长度限制（如 50 字符以内），超出时提示"备注内容过长，请精简"。 
2. **确定提交**：点击"确定"按钮，校验表单必填项完整性、合法性，校验通过则将活码信息（标题、域名、随机码、备注）提交至后端保存；校验不通过，在对应字段旁显示错误提示，阻止提交。 

#### （二）随机码生成与 URL 关联逻辑
1. **随机码生成**：弹窗加载时，触发随机码生成函数，具体实现要求：
   - **后端生成**：随机码由后端API生成并返回，前端仅负责展示和用户交互
   - **生成接口**：`GET /api/qrcode/generate-code`，响应格式：`{"code": "rJgMftSC", "timestamp": 1640995200}`
   - **重复处理**：后端自动处理重复码重新生成，确保返回的随机码全局唯一
   - **缓存机制**：生成的随机码临时缓存5分钟，避免用户未提交时的重复生成
   - **刷新功能**：前端提供"重新生成"按钮，用户可手动触发新随机码生成
2.  **URL 跳转规则**：后端保存活码信息时，需关联随机码与跳转 URL 逻辑。若随机码存在，将其拼接至基础 URL 后缀（如`https://xxx.com/` + 随机码），作为活码实际跳转链接；若随机码缺失（异常场景），默认跳转至谷歌页面（`https://www.google.com/`），需在后端配置默认跳转规则，确保链路清晰。 

### 三、非功能需求
1. **性能**：随机码生成需在 500 毫秒内完成，表单提交接口响应时间控制在 1 秒内，保障用户操作流畅性。 
2. **兼容性**：适配主流浏览器（Chrome、Firefox 等），弹窗样式、交互（如下拉框展开、按钮点击）在不同浏览器无明显差异。 
3. **可扩展性**：预留随机码生成规则扩展接口（如未来支持自定义随机码长度、字符集），便于功能迭代；URL 跳转逻辑可扩展（如支持多域名、多默认跳转页配置）。 

后续可结合活码管理其他环节（如列表展示、编辑关联），继续补充系统设计需求。 

---



## 系统设计需求 - 短码管理模块

### 一、概述
本模块用于实现短码的创建、查询、修改、删除等管理功能，需保障数据交互高效、权限控制精准、系统运行安全稳定，支撑短码全生命周期管理。 

### 二、功能需求
#### （一）数据交互与状态同步
1. **数据获取**：前端需通过异步请求（如 AJAX、RESTful API 调用），从后端接口定时或按需获取短码列表数据，包含编号、创建人、短码标题、短码链接、WS 类型、访问数、创建时间等字段，确保数据实时性。 
2. **操作同步**：执行修改、删除操作时，前端发起对应接口请求，后端处理数据变更后，前端需及时更新本地表格数据展示，保证前后端数据状态一致。需处理网络请求失败场景（如超时、接口报错），提供重试机制与友好的错误提示（如"操作失败，请重试"），避免数据不一致。 

#### （二）权限与安全控制
1. **操作权限**：基于用户角色（如管理员、普通用户），精准控制"修改" "删除"按钮权限。不同角色对应可操作的短码范围、操作权限不同（如普通用户仅能修改/删除自己创建的短码，管理员可操作全部）。后端需在接口层面进行权限校验，前端根据用户角色动态显示/隐藏操作按钮。 
2. **安全防护**：防范跨站请求伪造（CSRF），通过在请求头添加 token 等方式验证请求合法性；对用户输入（如修改短码标题等场景）进行过滤、校验，防止 SQL 注入、XSS 攻击，保障系统数据安全。 

#### （三）分页及筛选（扩展预备）
1. **基础分页**：当前实现简单分页，需支持可配置每页显示条数（如 10 条/页、20 条/页），前端根据分页参数（页码、每页条数）发起请求，后端返回对应数据切片，实现流畅的分页切换。 
2. **复杂筛选**：预留扩展能力，支持按创建时间范围（如起始时间 - 结束时间）、访问量区间（如 0 - 100、100 + 等）、WS 类型等条件组合筛选短码。前端需设计灵活的筛选条件组件，后端需优化数据库查询语句，支持多条件联合查询，确保大数据量下查询性能，避免页面渲染卡顿。 

### 三、非功能需求
1. **性能**：数据交互响应时间需控制在合理范围（如接口请求响应超时时间设置为 5 秒），分页、操作同步等功能在 1000 条以内数据量时，前端渲染无明显卡顿。 
2. **兼容性**：适配主流浏览器（Chrome、Firefox、Safari、Edge），确保功能正常使用、样式展示一致。 
3. **可维护性**：代码结构清晰，各功能模块解耦，便于后续新增功能（如短码统计分析、批量操作等）扩展与维护。 

后续可基于此框架，继续补充短码创建流程、访问记录管理等其他模块的系统设计需求。 

---

## 系统设计需求 - 访问记录模块

### 一、概述
本模块用于展示活码访问记录，需实现访问数据的准确呈现、关联跳转及批量操作，支撑对活码访问行为的追溯与管理，与活码管理模块协同，完善活码全流程管控。 

### 二、功能需求
#### （一）数据展示与关联
1. **访问记录呈现**：以表格形式展示访问记录数据，包含编号、活码 ID、访问时间、Wx/gq、IP、IP 位置字段。数据需从后端接口获取，确保实时、准确，表格布局清晰，字段对齐规范，便于用户查看。 
2. **关联跳转逻辑**：点击活码管理模块中"访问数"（如列表中访问数按钮），触发页面跳转至访问记录模块，同时携带对应活码筛选条件（如活码 ID），加载并展示该活码的访问记录，实现活码与访问记录的精准关联。 
3. **设备信息与用户代理解析**：
   - 功能实现：系统自动从HTTP请求中提取User-Agent信息，解析用户访问的设备类型（PC、移动设备、平板等）、操作系统（Windows、iOS、Android等）及其版本、浏览器类型（Chrome、Safari、Firefox等）及版本。
   - 数据展示：在访问记录表格中增加"设备类型"、"操作系统"、"浏览器"字段，直接展示解析结果。
   - 筛选功能：支持按设备类型、操作系统、浏览器类型进行筛选，便于分析特定设备用户的访问行为。
   - 解析策略：使用第三方User-Agent解析库（如user-agent-utils），确保解析准确率≥95%，解析失败时显示"未知设备/系统/浏览器"。

#### （二）批量操作
1. **全部删除功能**：提供"全部删除"按钮，点击后弹出二次确认弹窗（如提示"确认删除全部访问记录？删除后不可恢复"），确认后发起请求至后端，删除当前页面展示的所有访问记录（需校验用户权限，仅有权限用户可执行），删除成功后刷新表格，展示剩余数据（无数据时显示空状态）。'全部删除'操作仅在前端界面隐藏对应数据，后台记录的信息不会被实际删除，而是标记为软删除。后续统计、导出等操作默认不包含软删除数据（但是可以包含）。所有等级用户均遵循此逻辑。

#### （三）分页功能
1. **分页控制**：支持分页展示访问记录，提供每页条数选择（如 10 条/页、20 条/页）、页码切换（上一页、下一页、跳转指定页）功能。前端根据分页参数（页码、每页条数）向后端请求对应数据切片，后端需优化查询，保障大数据量下分页加载性能，避免页面卡顿。 

### 三、非功能需求
1. **性能**：访问记录数据加载（含分页、关联跳转场景）响应时间控制在 1 秒内，"全部删除"操作接口响应时间控制在 2 秒内，保障用户操作流畅。 
2. **兼容性**：适配主流浏览器（Chrome、Firefox 等），表格样式、交互（如按钮点击、分页切换）在不同浏览器显示及功能正常。 
3. **可维护性**：代码模块化，数据展示、操作逻辑、分页功能解耦，便于后续扩展（如新增按时间范围筛选、导出访问记录等功能）。 

### 四、与其他模块的关联
1. **与活码管理模块的关联**：
   - 访问记录作为活码使用效果的直接反馈，需与活码管理模块紧密关联。
   - 活码管理列表中的"访问数"字段需实时反映对应活码的访问记录总量。
   - 活码删除时，关联的访问记录应标记为软删除，保留历史数据。

2. **与数据统计模块的关联**：
   - 访问记录中的设备类型、操作系统、浏览器信息将作为数据统计模块的重要维度。
   - 数据统计模块可基于这些信息生成设备分布饼图、操作系统使用趋势、浏览器偏好分析等报表。
   - 结合IP归属地数据，可分析不同地区用户的设备使用习惯，为业务决策提供支持。
   - 支持按设备类型、系统版本筛选统计数据，便于针对特定用户群体优化服务。

可基于此，继续整合活码管理其他模块（如活码编辑、统计分析）的设计需求，完善整体系统方案。 

---



## 系统设计需求 - 短链接跳转规则模块

### 一、概述
本模块用于定义短链接跳转的核心规则，明确"系统生成的有效随机码"与"无效输入"对应的跳转目标，确保私密内容仅通过合法路径访问，无效场景统一导向预设页面（Google 首页）。

### 二、核心跳转规则
#### （一）触发场景与跳转目标
1. **正确场景（系统生成的有效随机码）**：
   - 触发条件：用户访问的短链接中，随机码是**系统在"添加活码"弹窗中自动生成**的，且该随机码已被系统记录（与指定链接绑定）。
   - 跳转目标：自动跳转到该随机码关联的"用户指定链接"（即创建活码时配置的目标链接）。

2. **错误场景（无效随机码/无随机码）**：
   - 触发条件：
     - 用户访问的短链接中，随机码是"用户自行输入的非系统生成内容"（如手动修改、乱填的字符串）；
     - 随机码未被系统记录（如已删除、从未创建过）；
     - 短链接后未携带任何随机码。
   - 跳转目标：统一自动跳转到 Google 首页（`https://www.google.com`）。

#### （二）随机码的有效性校验标准
1. **系统生成标识**：随机码必须是"添加活码"弹窗打开时，系统通过预设算法（如结合时间戳、随机字符生成）自动生成的字符串，且已成功保存到系统数据库中。
2. **唯一性绑定**：每个系统生成的随机码，在数据库中需唯一对应一条"用户指定链接"记录，确保跳转目标准确。
3. **不可篡改**：用户无法手动编辑或自定义随机码，仅能通过系统生成（避免人为构造有效随机码）。

### 三、流程设计
1. **随机码生成与存储**：
   - 用户打开"添加活码"弹窗 → 系统自动生成随机码 → 用户填写其他信息（标题、指定链接等）并提交 → 系统将"随机码"与"指定链接"绑定，存入数据库（标记为"系统生成有效"）。
2. **用户访问与跳转判断**：
   - 用户访问短链接（格式：`https://自定义域名/随机码`） → 系统提取链接中的随机码 → 校验该随机码是否为"系统生成且已绑定有效链接"：
     - 是 → 跳转到绑定的"用户指定链接"；
     - 否 → 跳转到 Google 首页。
3. **跳转执行**：系统根据校验结果，自动导向对应的目标链接，跳转过程需保持流畅，无明显延迟（响应时间控制在 1 秒内）。

### 四、短链接格式规范

1. **固定格式**：短链接统一为 "自定义域名 + / + 随机码"，例如 `https://yourdomain.com/rJgMftSC`（其中 `yourdomain.com` 为域名管理模块配置的前端域名，`rJgMftSC` 为系统生成的随机码）。
2. **格式约束**：域名部分必须是系统 "域名管理模块" 中已配置且处于 "启用" 状态的域名；随机码部分长度与字符集需符合系统生成规则（如 8 - 10 位，包含大小写字母和数字）。

### 五、非功能需求
1. **安全性**：随机码的生成算法需保证一定复杂度（如长度≥8位，包含大小写字母+数字），降低被暴力破解的概率；跳转过程中需屏蔽随机码在日志中的明文展示（脱敏处理），防止信息泄露。
2. **性能**：随机码校验及跳转响应时间需控制在 1 秒内，确保用户访问体验流畅，即使在高并发场景（如每秒 100 次访问）下，跳转逻辑无延迟。
3. **稳定性**：跳转规则需具备容错能力，如数据库临时不可用时，可缓存近期有效随机码至本地，确保核心跳转功能不中断；缓存同步周期不超过 5 分钟，避免数据不一致。
4. **可配置性**：错误场景的跳转目标（如 Google 首页）需支持配置化，超级管理员可在系统设置模块修改默认跳转地址，无需修改代码即可生效。

---

## 系统设计需求 - 域名管理模块

### 一、概述

本模块用于管理系统内购买的前端域名，聚焦域名的有效期管控、状态区分及过期处理，为活码创建提供 "在有效期内" 的域名资源，确保过期域名自动脱离业务流程（不展示给下级用户、支持超级管理员删除），与活码添加模块协同，简化域名生命周期管理。

### 二、功能需求

#### （一）域名基础信息管理

1. **域名添加**：
   - 操作权限：仅超级管理员可执行，添加时需填写：
     - 域名地址（必填，格式如"xxx.com"，唯一且符合域名规范，需校验格式有效性）；
     - 有效期（必填，精确到"年-月-日"，需晚于当前日期，如购买1年则填写"当前日期+1年"）；
   - 状态默认：添加后自动标记为"在用"（有效期内），系统每月自动校验所有域名的有效期，过期后状态自动变更为"过期"。
2. **域名列表展示（仅超级管理员可见）**：
   - 固定字段：域名地址、有效期、状态（"在用"/"过期"，"在用"用常规样式，"过期"用灰色弱化样式）、添加时间、备注、操作（删除）。
   - 筛选功能：支持按"状态"筛选（全部/在用/过期），便于快速定位过期域名。

#### （二）过期域名处理规则

1. **状态自动变更**：
   - 系统每月执行有效期校验，对"当前日期＞有效期"的域名，自动将状态从"在用"更新为"不可用"，无需人工操作。
   - 状态变更记录：自动生成操作日志（记录系统执行时间、域名地址、变更前状态、变更后状态），归入操作日志审计模块。
2. **对下级用户的隐藏规则**：
   - Boss、经理在"添加活码"的域名选择列表中，仅能看到"在用"状态的域名（有效期内），"不可用"域名完全隐藏，不展示也不可选。
   - 已关联不可用域名的活码：活码创建时域名处于有效期内，后期域名过期后，活码本身可正常使用（短链接跳转不受影响），但下级用户（Boss、经理）无法通过该不可用域名创建新活码。已关联不可用域名的活码，在活码列表中将被特殊标识为"已过期域名的活码"。实际跳转是否可用取决于域名本身的DNS状态。
3. **域名状态管理**：
   - 操作权限：仅超级管理员可修改域名状态。
   - 状态管理：超级管理员可更改域名状态为"不可用"，前端页面不再显示不可用域名。不可用后的域名仍保留在系统中，确保历史活码数据的完整性。明确"不可用"仅影响新业务和前端展示，历史活码能否跳转取决于域名DNS实际状态。

#### （三）与其他模块的关联

1. **与活码添加模块关联**：
   - 活码创建时，域名选择列表仅加载"在用"状态域名（有效期内往前提前5天，提高容错率），过期域名不展示，确保下级用户（Boss、经理）只能选择未过期域名。域名状态有效期按照DNS解析进行展示在超级管理员的域名管理界面，域名需"有效期内往前提前5天"这个要求是为了规避到期时一些风险操作，所以在其他用户的域名选择列表中有效期往前提5天。
   - 无需关联校验：因过期域名已自动隐藏，且下级用户无法选择，无需额外校验域名状态对活码创建的影响。
2. **与短链接跳转模块关联**：
   - 域名过期后，已关联该域名的活码短链接仍可正常跳转（按随机码有效性判断），不因域名过期改变跳转规则（因过期域名直接换新，无需限制历史链接）。

### 三、非功能需求

1. **性能**：
   - 域名添加时的格式校验响应时间≤500ms，确保操作流畅。
   - 每月的有效期校验任务执行时间≤1分钟（针对≤100个域名的场景），不影响系统其他功能运行。
2. **安全性**：
   - 域名删除操作需二次确认，防止误操作；删除日志永久保存，支持超级管理员追溯。
   - 下级用户（Boss、经理）无法通过技术手段查看或调用过期域名，确保域名状态管控严格。
3. **可维护性**：
   - 有效期校验的执行时间（如凌晨0点）支持超级管理员在系统设置中调整，适配业务时间需求。
   - 域名列表字段（如备注长度）可通过配置文件修改，便于后续扩展。

### 四、模块简化说明

- 移除"启用/禁用"状态：仅通过"在用（有效期内）/过期（超期）"区分状态，简化管理逻辑。
- 取消关联校验：因过期域名直接换新，无需校验域名与活码的关联关系，删除操作无限制。
- 聚焦"过期即隐藏"：通过有效期自动管控下级用户（Boss、经理）的域名可见范围，减少人工干预。

------

## 系统设计需求 - 权限与数据管理模块

### 一、概述
本模块实现系统的权限控制与数据隔离，支撑超级管理员、Boss、经理等不同角色的操作权限及数据可见范围，保障系统安全与业务流程顺畅。 

### 二、功能需求
#### （一）角色与权限管理
1. **角色定义**：
    - 超级管理员：最高权限角色，可进行系统全局配置，包括添加用户、创建角色、分配权限（细化到按钮级，如"添加活码""删除访问记录"按钮权限）等操作。
    - Boss：管理下属经理，可查看下属经理数据、配置下属权限（在超级管理员授权范围内），能看到其管理范围内的活码、访问记录等数据。 
    - 经理：基础操作角色，仅能查看和操作自己的数据（如自己创建的活码、自己相关的访问记录）。 
2. **权限分配**：
    - 超级管理员通过权限配置界面，为不同角色（Boss、经理）分配功能权限，可精确控制每个角色能访问的页面、可操作的按钮（如是否允许Boss删除经理创建的活码）。权限配置需支持批量设置与单个调整，权限变更后实时同步到对应角色用户的操作界面。
    - Boss在超级管理员授权的权限范围内，可对下属经理进行权限二次分配（如调整经理是否可编辑活码标题权限），分配结果需同步反馈给超级管理员查看。超级管理员配置为最高优先，Boss仅能在授权范围内调整权限，且不能覆盖超级管理员的限制。所有权限变更均有日志记录，便于追溯。
    - 权限变更后对用户会话做即时校验，刷新权限，确保权限变更的实时生效。

#### （二）用户管理
1. **用户创建**：
    - 超级管理员通过用户管理界面，录入新用户信息（含昵称、工号、所属部门、角色等），创建经理或Boss账号。创建时需校验工号唯一性，支持批量导入用户（通过 Excel 等格式），导入后自动关联初始角色与基础权限。 
    - Boss在授权范围内，可申请创建下属经理账号，提交申请后需经超级管理员审批，审批通过后完成用户创建，默认分配经理角色及基础权限。 
2. **用户信息维护**：
    - 超级管理员可修改所有用户的信息（角色、权限、所属部门等），Boss可修改下属经理的部分信息（如昵称、状态、密码、备注），经理仅能修改自身的非关键信息（如昵称）。信息修改需记录操作日志（谁修改、何时修改、修改内容），便于追溯。 

#### （三）数据可见范围
1. **活码与访问记录数据**：
    - 超级管理员：可查看系统内所有活码（含各经理创建的）、所有访问记录，无数据范围限制。 
    - Boss：可查看其管理下属经理创建的活码、对应的访问记录，以及自己创建的活码和访问记录。支持按经理、活码标题等维度筛选数据。 
    - 经理：仅能查看自己创建的活码及对应的访问记录，无法查看其他经理创建的数据。 

### 三、非功能需求
1. **性能**：权限校验、数据筛选查询需高效执行，在 1000 条以上数据量时，页面加载与操作响应时间控制在 1 秒内，保障多角色用户操作流畅。 
2. **安全性**：权限控制需严格，防止越权访问（如经理通过技术手段访问其他经理数据）。用户密码等敏感信息需加密存储，操作日志需完整、不可篡改，用于安全审计。 
3. **兼容性**：权限与数据展示需适配不同角色的操作终端（PC 端、移动端），确保功能与体验一致。 
4. **可维护性**：角色、权限、数据范围的配置需灵活，便于后续新增角色（如高级Boss）、调整权限规则时快速迭代，降低维护成本。 

该模块与活码管理、访问记录等模块深度关联，后续补充其他模块设计时，需同步考虑权限与数据范围的适配，比如活码创建时关联创建人角色，访问记录关联所属经理等，保障系统整体逻辑闭环。

---

## 系统设计需求 - 管理员列表及用户管理模块

### 一、功能定位
实现多角色（超级管理员、Boss、经理）的用户信息管理，覆盖列表展示、新增、编辑流程，严格区分角色权限，保障不同层级用户操作与数据可见范围精准可控，支撑系统权限体系落地。

### 二、功能需求
#### （一）管理员列表展示
1. **数据字段**：
    - 固定展示：编号、用户昵称、账号、上级 ID、邮箱、版本、角色、状态、开通时间、操作。
    - 数据规则：
        - 角色字段：区分"超级管理员""Boss""经理"，不可手动编辑。
        - 状态字段：支持"正常""离线""禁用"三种状态标识，"禁用"状态用户将移入折叠列表，历史数据归属保留，防止数据丢失。"离线"和"禁用"状态仅可以通过上级管理员进行更改。禁用后不可再分配新业务，历史数据只读，统计报表默认不计入禁用用户，必要时可由上级手动转移数据。禁用用户禁止其一切动态操作。只有通过上级管理进行状态恢复之后，才可再次进行操作。数据仍然保留其名下。
        - 上级 ID 字段：Boss及经理角色需关联上级Boss ID，超级管理员上级 ID 为空。
2. **数据可见范围**：
    - 超级管理员：查看系统内全部管理员数据，无范围限制。
    - Boss：仅查看自身管理范围内的经理数据（通过上级 ID 关联匹配），无法查看其他Boss及超级管理员数据。
    - 经理：无管理员列表查看权限（或仅能查看自身信息，需结合权限配置）。

#### （二）添加用户功能
1. **触发与弹窗**：
    - 触发：点击"添加用户"按钮唤起弹窗，按钮仅超级管理员、Boss可见（经理无此权限，也不显示）。
    - 弹窗字段：用户昵称（必填，字符长度 2 - 10）、账号（必填，唯一标识，字符长度 4 - 20 ，支持字母、数字、下划线，但无硬性具体要求）、密码（必填，首次创建时设置，字符长度 6 - 20 ，需包含字母、数字）、邮箱（选填，符合邮箱格式校验）、角色（超级管理员仅能选择"Boss""经理"；Boss仅能选择"经理"）、状态（默认"正常"，不可编辑）、备注（选填，字符长度 0 - 50）。
2. **权限控制**：
    - 超级管理员：可添加"Boss""经理"角色用户，添加"Boss"时上级 ID 为空；添加"经理"时需指定上级Boss ID（关联已有Boss数据）。
    - Boss：仅可添加"经理"角色用户，上级 ID 固定为当前Boss ID ，提交时自动赋值。

#### （三）修改管理员功能
1. **弹窗交互**：
    - 触发：点击操作列"修改信息"按钮唤起弹窗，按钮权限与数据可见范围关联（超级管理员可修改所有；Boss仅可修改下属经理）。
    - 字段编辑规则：
        - 用户昵称：可编辑，字符长度 2 - 10 ，修改后实时同步列表展示。
        - 账号：不可编辑（作为唯一标识，保障数据关联稳定性）。
        - 是否修改密码：开关控件，开启后显示密码输入框（需重新输入符合规则的密码），关闭则不修改密码。
        - 邮箱：可编辑，符合邮箱格式校验。
        - 状态：超级管理员可修改（切换"正常""离线""禁用"）；Boss仅能查看，不可编辑。
        - 备注：可编辑，字符长度 0 - 50 。
2. **权限校验**：
    - 超级管理员：可修改所有字段（含角色、状态），修改后覆盖原有数据。普通用户不可编辑角色，超级管理员可通过专用操作变更角色，并记录日志。系统不支持经理变为Boss的角色变更，角色一旦设定不可变更。
    - Boss：仅可修改下属经理的用户昵称、邮箱、备注、密码（通过"是否修改密码"开关），角色、状态、上级 ID 字段不可见且不可编辑。

#### （四）数据同步与校验
1. **数据同步**：添加、修改操作完成后，管理员列表需实时刷新，确保不同角色看到最新数据，无延迟。
2. **唯一性校验**：
    - 账号字段：新增、修改时（若开放修改需严格校验），与数据库已有账号对比，确保唯一，冲突时提示"该账号已存在，请更换"。
    - 邮箱字段：选填但需校验格式（@ 符号、域名后缀），格式错误提示"邮箱格式不正确"。
3. **密码规则校验**：密码字段需包含字母、数字，长度 6 - 20 ，不符合规则提示"密码需 6 - 20 位，包含字母与数字"。

### 三、非功能需求
#### （一）性能
1. 管理员列表加载：分页加载（默认 10 条/页）时，接口响应时间 ≤ 500ms ；全量数据（≤ 1000 条）加载，响应时间 ≤ 1s。
2. 添加、修改操作：接口响应时间 ≤ 500ms ，操作完成后列表刷新无明显延迟。

#### （二）兼容性
1. 支持 Chrome（最新版）、Firefox（最新版）、Edge（最新版）浏览器，弹窗、表格样式统一，交互（按钮点击、开关切换、下拉选择）无异常。

#### （三）数据安全
1. 密码传输：采用 HTTPS 加密传输，存储时加密处理（如 MD5 加盐 ，或更安全的加密算法），保障数据安全。
2. 操作日志：记录添加、修改操作的执行角色、操作时间、操作内容（如"超级管理员修改用户 xxx 状态为 离线"），日志可追溯、不可篡改。

#### （四）可维护性
1. 字段规则（如字符长度、格式）、角色权限配置，需通过配置文件或后台管理界面实现可配置化，便于后续调整业务规则，无需修改代码。 

此需求文档聚焦功能边界与规则，可直接作为开发、测试依据，后续可扩展对接权限管理、日志模块，完善系统整体流程。

---

## 系统设计需求 - IP归属地解析模块

### 一、概述
本模块用于解析访问记录中的IP地址，获取其地理位置信息，提升数据分析的准确性和可视化能力，为活码访问行为分析提供地域维度支持。

### 二、功能需求
#### （一）IP归属地解析核心
1. **技术方案选择**：
   - 系统集成MaxMind GeoLite2数据库，作为IP地址解析的基础库。
   - 解析精度支持国家、省份、城市三级地理信息，满足不同粒度的数据分析需求。
   - 支持IPv4/IPv6双协议栈，适应网络环境变化。

2. **解析流程设计**：
   - 用户访问活码时，系统记录其IP地址。
   - 后端调用GeoLite2库进行解析，获取归属地信息。
   - 解析结果存储于访问记录表中，便于后续查询和展示。
   - 若解析失败（如内网IP、无法识别的IP），归属地字段显示为"未知地区"。

#### （二）数据展示与应用
1. **访问记录展示**：
   - 访问记录表格中新增"IP归属地"字段，直接展示解析结果（如"中国-广东-深圳"）。
   - 支持按归属地筛选、统计访问数据，便于分析特定地区的访问情况。

2. **统计分析应用**：
   - 基于IP归属地数据，生成访问热力图，直观展示不同地区的访问密度。
   - 提供地区访问排行榜，展示访问量最高的前N个地区，辅助业务决策。

#### （三）数据库维护与更新
1. **GeoLite2数据库更新**：
   - 系统每月自动检查并更新GeoLite2数据库，确保地理位置解析的准确性。
   - 更新过程在系统低峰期执行，避免影响正常业务。
   - 更新完成后记录日志，包含更新时间、版本号、更新结果等信息。

2. **历史数据处理**：
   - 数据库更新后，支持对历史访问记录的IP地址重新解析，提升历史数据准确性。
   - 重新解析任务支持手动触发，仅超级管理员有权限执行。

### 三、非功能需求
1. **性能要求**：
   - 单次IP解析响应时间≤200ms，不影响活码访问记录的正常保存。
   - 批量处理时（如历史数据重新解析）支持异步队列，避免阻塞主业务流程。
   - 高并发场景下（如每秒100次访问）解析服务稳定，无明显延迟。

2. **可靠性**：
   - IP解析服务异常时，不影响活码正常访问和记录保存，仅归属地显示为"未知"。
   - 提供解析失败的重试机制，最多尝试3次，减少解析失败率。

3. **可维护性**：
   - 解析库与主系统解耦，支持未来更换为其他地理位置服务。
   - 解析过程产生的日志独立存储，便于问题追溯和性能优化。
   - 支持配置化管理解析精度、缓存策略、更新周期等参数。


### 四、与其他模块的关联
1. **与访问记录模块的关联**：
   - 访问记录保存时自动触发IP解析，解析结果与访问记录一同保存。
   - 点击对应活码的访问记录查询时，同步展示IP归属地信息，无需二次查询。

2. **与数据统计模块的关联**：（暂时不添加该模块）
   - 提供基于地域维度的数据聚合接口，支持数据统计模块生成地域分布报表。
   - 支持多维度交叉分析，如特定时间段内的地域访问分布。
   - 后续不定时会补充统计模块，需注意与现有数据结构、软删除、禁用用户等逻辑的兼容。保持数据结构的可扩展性，提前预留接口和标记字段。

---

## 系统设计需求 - 多类型链接轮询跳转模块

### 一、概述
本模块用于管理和轮询多种类型的链接数据（包括WhatsApp、Telegram、网址等），每个活码关联自己的专属链接数据集合，确保每次活码链接访问时，根据预设的轮询规则（新批次优先），将对应数据动态拼接到相应URL后进行跳转，以实现流量的均衡分配和新业务的优先处理。

### 二、功能需求
#### （一）多类型链接数据管理核心
1. **类型选择与数据录入**：
   - 系统提供类型选择下拉框，支持以下类型：
     - Ws（默认选择）：用于管理WhatsApp账号数据
     - 电报telegram：用于管理Telegram号码数据
     - 网址：用于管理URL地址数据
   - 界面根据选择的类型动态显示对应的输入提示文字：
     - 选择"Ws"时，显示"账号"
     - 选择"电报telegram"时，显示"号码"
     - 选择"网址"时，显示"URL"
   - 系统需提供界面，支持用户手动输入或批量导入多行数据，根据类型不同，可以按行分割、一行一个，也可以一次录入多个账号。
   - 数据存储时需记录添加顺序或时间戳、批次ID及数据类型，以支持批次管理和优先级控制。
   - **每个活码关联自己的链接数据集合**：每个活码拥有独立的链接数据，不同活码之间的链接数据相互隔离，不会混用。

2. **批次管理与轮询机制**：
   - 每次添加链接数据时，系统自动将其标记为一个新批次。
   - 新添加的批次可以设置优先轮询次数（例如轮询10次、20次等）。
   - 当用户点击活码链接时，系统会从该活码关联的链接数据中，按照批次优先级规则选择一个记录进行跳转。
   - 系统优先从最新批次中按顺序选择链接数据，每次使用都记录轮询次数。
   - 当新批次中的第一个数据达到设定的轮询次数时，该批次将与之前的批次合并，作为一个整体进行常规轮询。
   - 如果再添加新批次，重复上述过程，确保新添加的批次总是获得优先轮询权。

3. **批次优先级管理**：
   - 采用简单的"后进先出"原则 - 最新添加的批次优先级最高。
   - 当最新批次完成设定的轮询次数后，自动转向次新批次。
   - 所有优先批次都完成后，再合并到常规批次进行轮询。
   - 允许管理员调整未完成批次的剩余轮询次数。
   - 在管理界面直观显示各批次的状态和轮询进度。

#### （二）URL 拼接与跳转
1. **URL 拼接**：
   - 系统根据数据类型，将轮询到的数据拼接至对应的URL格式：
     - Ws类型：拼接至`api.whatsapp.com/send/?phone=`，如`https://api.whatsapp.com/send/?phone=254751076496`
     - 电报telegram类型：拼接至`https://t.me/`，如`https://t.me/username`
     - 网址类型：直接使用录入的URL地址，如`https://example.com`

2. **跳转执行**：
   - 拼接完成后，系统自动执行跳转操作，将用户重定向到生成的链接。
   - 跳转过程需流畅，无明显延迟，确保用户体验。

#### （三）数据列表展示与维护
1. **列表展示**：
   - 提供界面展示已添加的数据列表，包括其类型、批次信息、添加时间、轮询状态等，便于用户管理。
   - 列表应支持分页显示，避免数据量过大时加载缓慢。
   - 列表应显示数据类型，便于用户区分不同类型的数据。
   - 清晰展示各批次的状态（优先轮询中/已合并到常规批次）和轮询进度。

2. **数据维护**：
   - 支持对列表中已添加数据的修改和删除操作。
   - 删除操作需提供二次确认，防止误操作。
   - 修改操作应确保数据格式的正确性，根据不同类型进行相应的格式校验。
   - 支持调整未完成批次的剩余轮询次数。

#### （四）批量删除与全部删除功能
1. **批量删除**：
   - 系统需在数据列表界面，支持用户通过以下方式选择待删除数据：
     - 复选框方式：勾选界面上的任意多条数据（可为第1、4、7、11条等非连续数据）。
     - 手动输入方式：提供批量输入框，允许用户直接输入多个账号、号码或URL（根据当前选择的类型），支持逗号、空格或换行符分隔，系统自动匹配这些数据并删除对应记录，提高批量操作效率。
   - 用户确认选择后，点击"批量删除"按钮，系统弹出二次确认提示（如"确认删除选中的X条数据？删除后不可恢复"），确认后将所选数据从列表及存储中移除。
   - 删除操作应支持任意组合（非顺序、非连续），并在删除后自动刷新列表，保持数据一致性。
   - 系统需对手动输入的数据进行有效性校验（是在删除之后），对不存在的数据给予友好提示（如"账号:xxx,yyy已经不存在"）。
   - 删除操作需有日志记录，便于后续追溯。

2. **全部删除**：
   - 系统需提供"全部删除"按钮，允许用户一键清空当前活码关联的所有链接数据。
   - 点击"全部删除"时，需弹出二次确认提示（如"确认删除全部数据？删除后不可恢复"），确认后清空所有数据。
   - 删除后，列表应显示为空状态，并同步更新存储，确保数据彻底移除。
   - 该操作同样需记录操作日志。

3. **操作权限与安全性**：
   - 仅有权限的用户可进行批量删除和全部删除操作。
   - 所有删除操作均需二次确认，防止误操作。
   - 删除操作为物理删除，数据不可恢复，需在操作前明确提示用户。

4. **接口与前端交互**：
   - 前端需支持多选、全选、反选等交互方式，便于用户灵活选择待删除数据。
   - 后端需提供批量删除和全部删除的接口，支持接收多个ID进行批量处理。
   - 删除成功后，前端需自动刷新列表，确保展示与实际数据一致。

### 三、非功能需求
1. **性能要求**：
   - 数据的轮询获取和 URL 拼接跳转响应时间需控制在 500ms 内，保障用户点击活码链接后的即时响应。
   - 数据添加和维护操作的接口响应时间应在 1 秒内完成，确保管理后台操作流畅。

2. **可靠性**：
   - 轮询机制应具备容错能力，即使在系统重启或异常情况下也能恢复到正确的轮询状态。
   - 数据存储应持久化，防止数据丢失。
   - 若数据列表为空，应有默认的错误处理或提示，避免空指针异常或跳转失败。

3. **可维护性**：
   - 轮询逻辑应与前端展示解耦，便于独立维护和升级。
   - 支持配置化管理轮询策略，便于未来业务调整。
   - 相关操作应有日志记录，便于问题追溯和审计。

### 四、与其他模块的关联
1. **与活码管理模块的关联**：
   - 活码创建时，需提供选项或配置入口，允许活码关联到链接轮询模块，并选择链接类型。
   - 在活码管理界面，可以进入该活码的链接数据管理页面，添加和管理该活码专属的链接数据。
   - 活码链接生成时，需调用本模块的接口获取当前轮询到的数据进行 URL 拼接。

2. **与访问记录模块的关联**：
   - 每次通过链接成功跳转后，应在访问记录中记录本次跳转所使用的具体数据及其类型，以便进行数据分析和追溯。

---

## 额外模块

#### 操作日志审计模块

- **核心必要性**：现有 "数据安全" 需求提到需记录操作日志，但未明确日志的存储、查询、展示规则，需独立模块保障操作可追溯。
- 核心功能：
  - 日志字段：操作人（账号 / 昵称）、操作角色、操作时间（精确到秒）、操作类型（添加用户 / 修改权限 / 分配客户 / 删除记录等）、操作对象（如 "用户 xxx""客户 xxx""活码 xxx"）、操作详情（变更前 / 后的值，如 "密码从***变为***""客户状态从'未跟进'变为'跟进中'"）、IP 地址。
  - 查询权限：超级管理员可查询所有日志；Boss仅可查询自身及下属经理的操作日志；经理仅可查询自身操作日志。
  - 存储规则：日志永久存储，不可删除 / 修改，支持按时间、操作人、操作类型筛选导出。

------

#### 数据统计与分析模块

- **核心必要性**：现有 "访问记录模块" 仅记录基础访问数据，缺乏统计分析能力，无法支撑业务决策，需补充该模块实现数据可视化。
- 核心功能：
  - 统计维度：
    - 活码 / 短码维度：访问总量、今日 / 昨日访问量、地域分布（按 IP 解析）、设备类型（PC / 移动端）、访问峰值时间。
    - 经理维度：Boss可查看下属经理的客户跟进量、成交转化率；超级管理员可查看全系统经理 / Boss的业绩排行。
    - 客户维度：各来源渠道的客户转化率、新增客户数趋势图（日 / 周 / 月）。
    - 设备与用户体验维度：提供设备类型分布（PC、移动设备、平板等）、操作系统占比（Windows、iOS、Android等）、浏览器使用情况（Chrome、Safari、Firefox等）的可视化统计图表，支持按时间段、地区、活码分组等多维度交叉分析，帮助优化针对不同设备用户的访问体验。
  - 权限控制：统计数据可见范围与 "数据可见规则" 一致（Boss仅看下属相关数据）。

------

#### 密码与账号安全模块

- **核心必要性**：现有 "添加用户" 仅设置初始密码，未覆盖密码重置、账号锁定等安全场景，需模块保障账号安全。
- 核心功能：
  - 密码重置：经理可自主修改密码（需验证原密码）；Boss / 超级管理员可强制重置下属密码（重置后生成临时密码，经理首次登录需修改）。
  - 登录日志：记录账号每次登录时间、IP、设备信息，经理可查看自身登录记录，Boss / 超级管理员可查看下属登录记录。

---

# 三类用户

```plaintext
系统权限总览  
├─ 超级管理员  
│  ├─ 用户管理  
│  │  ├─ 查看：所有用户（超级管理员、Boss、经理）数据  
│  │  ├─ 添加：可创建"Boss""经理"角色用户  
│  │  ├─ 编辑：可修改所有用户的全部信息（含角色、状态、密码）  
│  │  └─ 删除：可删除所有用户（需无关联业务数据）  
│  ├─ 角色与权限管理  
│  │  ├─ 创建角色：自定义新角色及权限  
│  │  ├─ 分配权限：为所有角色分配权限（细化到按钮级）  
│  │  └─ 权限修改：调整任意角色的权限配置  
│  ├─ 活码/短码管理  
│  │  ├─ 查看：所有活码、短码及关联数据（含访问记录）  
│  │  ├─ 创建：添加活码（选择任意启用域名）  
│  │  ├─ 编辑：修改所有活码的全部信息（含标题、域名、随机码关联链接）  
│  │  └─ 删除：删除所有活码/短码（删除后对应短链接失效）  
│  ├─ 域名管理  
│  │  ├─ 查看：所有域名（含启用/禁用状态、关联活码数）  
│  │  ├─ 添加：新增域名（需通过格式与有效性校验）  
│  │  ├─ 编辑：修改域名状态（启用/禁用）、备注  
│  │  └─ 删除：删除未关联活码的域名  
│  ├─ 数据统计与分析  
│  │  └─ 查看：全系统所有维度统计数据（活码访问、经理业绩）  
│  └─ 操作日志审计  
│     └─ 查看：所有操作日志（含用户管理、权限变更等）  
│  
├─ Boss  
│  ├─ 用户管理  
│  │  ├─ 查看：自身及下属经理数据  
│  │  ├─ 添加：仅创建"经理"角色用户（上级 ID 绑定为当前Boss）  
│  │  ├─ 编辑：修改下属经理的部分信息（昵称、邮箱、密码、备注）  
│  │  └─ 删除：删除下属经理（需无关联活码）  
│  ├─ 活码/短码管理  
│  │  ├─ 查看：自身及下属经理创建的活码、短码及访问记录  
│  │  ├─ 创建：添加活码（选择任意启用域名）  
│  │  ├─ 编辑：修改自身及下属经理创建的活码信息（不含随机码）  
│  │  └─ 删除：删除自身及下属经理创建的活码/短码  
│  ├─ 数据统计与分析  
│  │  └─ 查看：自身及下属经理相关的统计数据（活码访问）  
│  └─ 操作日志审计  
│     └─ 查看：自身及下属经理的操作日志  
│  
└─ 经理  
   ├─ 活码/短码管理  
   │  ├─ 查看：自身创建的活码、短码及访问记录  
   │  ├─ 创建：添加活码（选择启用域名）  
   │  ├─ 编辑：修改自身创建的活码信息（不含随机码）  
   │  └─ 删除：删除自身创建的活码/短码  
   └─ 个人信息管理  
      ├─ 查看：自身账号信息（昵称、邮箱、角色等）  
      └─ 编辑：修改自身密码、昵称、邮箱、备注  
```

# 难点

1. 来访 IP 如何记录，以及解析出来访的地址；（使用 MaxMind GeoLite2 来解析）
2. 


# 完整业务流程

