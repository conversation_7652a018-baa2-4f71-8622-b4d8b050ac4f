# 企业级活码管理系统 ER 图

## 版本说明

本文档包含两个版本的ER图：
1. **企业级版本** - 更完整的实体关系设计，包含详细的索引策略和性能优化
2. **简化版本** - 更直观的实体关系展示，便于理解业务逻辑

---

## 企业级版本 ER 图

### 实体关系图

```mermaid
erDiagram
    USERS {
        bigint id PK
        varchar username
        varchar email
        varchar password_hash
        enum role
        enum status
        bigint created_by FK
        timestamp last_login_at
        timestamp created_at
        timestamp updated_at
    }

    DOMAINS {
        bigint id PK
        varchar domain
        date expiry_date
        enum status
        text remark
        timestamp created_at
        timestamp updated_at
    }

    SHORT_CODES {
        bigint id PK
        varchar title
        varchar short_code
        enum type
        enum status
        text description
        bigint creator_id FK
        bigint domain_id FK
        bigint visits
        timestamp created_at
        timestamp updated_at
    }

    LINK_BATCHES {
        bigint id PK
        varchar name
        bigint short_code_id FK
        int priority
        int priority_count
        int used_count
        enum status
        timestamp created_at
        timestamp updated_at
    }

    LINK_DATA {
        bigint id PK
        bigint batch_id FK
        bigint short_code_id FK
        text content
        bigint usage_count
        enum status
        timestamp created_at
        timestamp updated_at
    }

    ACCESS_RECORDS {
        bigint id PK
        bigint short_code_id FK
        varchar ip_address
        text user_agent
        enum device_type
        varchar location
        text referer
        text target_content
        timestamp created_at
    }

    OPERATION_LOGS {
        bigint id PK
        bigint user_id FK
        varchar user_name
        enum user_role
        varchar action
        varchar target_type
        bigint target_id
        varchar target_name
        text details
        varchar ip_address
        enum status
        timestamp created_at
    }

    USERS ||--o{ USERS : "created_by"
    USERS ||--o{ SHORT_CODES : "creator_id"
    USERS ||--o{ OPERATION_LOGS : "user_id"

    DOMAINS ||--o{ SHORT_CODES : "domain_id"

    SHORT_CODES ||--o{ LINK_BATCHES : "short_code_id"
    SHORT_CODES ||--o{ LINK_DATA : "short_code_id"
    SHORT_CODES ||--o{ ACCESS_RECORDS : "short_code_id"

    LINK_BATCHES ||--o{ LINK_DATA : "batch_id"
```

### 关系说明

#### 1. 用户关系 (USERS)
- **自引用关系**: `created_by` 字段引用同表的 `id`，表示用户的创建者
- **层级结构**: 超级管理员 → Boss → 经理

#### 2. 短码核心关系 (SHORT_CODES)
- **创建者关系**: 每个短码都有一个创建者 (`creator_id`)
- **域名关系**: 短码可以关联一个域名 (`domain_id`)，可为空
- **一对多关系**: 一个短码可以有多个批次和访问记录

#### 3. 链接轮询关系
- **批次管理**: `LINK_BATCHES` 管理轮询批次
- **链接数据**: `LINK_DATA` 存储具体的轮询链接
- **双重关联**: 链接数据同时关联批次和短码

#### 4. 访问统计关系 (ACCESS_RECORDS)
- **记录访问**: 每次访问都会产生一条记录
- **统计分析**: 用于生成各种访问统计报表

#### 5. 审计日志关系 (OPERATION_LOGS)
- **操作记录**: 记录用户的所有操作行为
- **可选用户**: `user_id` 可为空（系统操作）

### 索引策略

#### 主要查询索引
1. **用户查询**: `email`, `username`, `role`, `status`
2. **短码查询**: `short_code`, `creator_id`, `type`, `status`
3. **访问记录**: `short_code_id + created_at` 组合索引
4. **操作日志**: `user_id + created_at` 组合索引

#### 复合索引
- `short_codes(creator_id, type)`: 按创建者和类型查询
- `access_records(short_code_id, created_at)`: 时间范围统计
- `link_data(batch_id, status)`: 批次状态查询

### 数据完整性

#### 外键约束
- 启用外键约束确保数据一致性
- 级联删除：删除短码时同时删除相关批次和访问记录
- 设置NULL：删除域名时不影响短码，仅置空关联

#### 业务规则
1. **短码唯一性**: `short_code` 字段全局唯一
2. **邮箱唯一性**: `email` 字段全局唯一
3. **域名唯一性**: `domain` 字段全局唯一
4. **状态枚举**: 使用枚举类型确保状态值有效性

### 性能优化

#### 分区策略
- `access_records` 表可按时间分区
- `operation_logs` 表可按月份分区

#### 存储优化
- 使用 InnoDB 引擎支持事务
- 设置合适的字符集 utf8mb4
- 定期清理历史数据

#### 查询优化
- 为常用查询字段建立索引
- 避免全表扫描
- 使用分页查询减少数据传输

---

## 简化版本 ER 图

### 实体关系图概览

以下ER图展示了短链接系统的主要实体及其关系：

```mermaid
erDiagram
    USER {
        bigint id PK
        varchar nickname
        varchar username
        varchar password
        varchar email
        varchar role
        tinyint status
        bigint parent_id FK
        datetime created_at
        datetime updated_at
        varchar remark
    }
    
    ROLE_PERMISSION {
        bigint id PK
        varchar role
        varchar permission
    }
    
    DOMAIN {
        bigint id PK
        varchar domain
        date expire_date
        tinyint status
        datetime created_at
        varchar remark
    }
    
    QRCODE {
        bigint id PK
        varchar title
        bigint domain_id FK
        varchar random_code
        bigint creator_id FK
        varchar remark
        datetime created_at
        datetime updated_at
    }
    
    SHORT_URL {
        bigint id PK
        bigint qrcode_id FK
        varchar short_code
        varchar url
        varchar ws_type
        bigint creator_id FK
        int visit_count
        datetime created_at
        datetime updated_at
    }
    
    VISIT_LOG {
        bigint id PK
        bigint qrcode_id FK
        bigint short_url_id FK
        datetime visit_time
        varchar ip
        varchar ip_location
        varchar device_type
        varchar os_info
        varchar browser_info
        varchar user_agent
        bigint polling_data_id FK
        tinyint deleted
    }
    
    POLLING_DATA {
        bigint id PK
        bigint qrcode_id FK
        varchar type
        varchar value
        varchar batch_id
        int priority
        int remaining_count
        int total_count
        tinyint is_priority
        int order_num
        datetime created_at
        datetime updated_at
    }
    
    OPERATION_LOG {
        bigint id PK
        bigint operator_id FK
        varchar operator_role
        varchar action
        varchar object_type
        bigint object_id
        text detail
        varchar ip
        datetime created_at
    }
    
    LOGIN_LOG {
        bigint id PK
        bigint user_id FK
        datetime login_time
        varchar ip
        varchar device_info
        tinyint status
        varchar remark
    }
    
    SYSTEM_CONFIG {
        bigint id PK
        varchar config_key
        text config_value
        varchar description
        datetime updated_at
        bigint updated_by FK
    }

    USER ||--o{ USER : "上级-下级"
    USER ||--o{ QRCODE : "创建"
    USER ||--o{ SHORT_URL : "创建"
    USER ||--o{ OPERATION_LOG : "操作"
    USER ||--o{ LOGIN_LOG : "登录记录"
    USER ||--o{ SYSTEM_CONFIG : "更新配置"
    
    DOMAIN ||--o{ QRCODE : "使用"
    
    QRCODE ||--o{ SHORT_URL : "关联"
    QRCODE ||--o{ VISIT_LOG : "被访问"
    QRCODE ||--o{ POLLING_DATA : "包含"
    
    SHORT_URL ||--o{ VISIT_LOG : "被访问"
    
    POLLING_DATA ||--o{ VISIT_LOG : "被使用"
```

### 实体关系说明

#### 用户与权限
- **用户(USER)** - 存储系统用户信息，包括超级管理员、Boss和经理
- **角色权限(ROLE_PERMISSION)** - 存储角色与权限的对应关系
- **登录日志(LOGIN_LOG)** - 记录用户登录信息，包括时间、IP和设备信息
- 用户之间可形成上下级关系，Boss管理多个经理，超级管理员无上级

#### 活码与短链
- **域名(DOMAIN)** - 存储可用的前端域名信息，对应数据库表 `domains`
- **活码(SHORT_CODE)** - 存储活码基本信息，关联域名和创建用户，对应数据库表 `short_codes`
- **链接批次(LINK_BATCH)** - 存储链接批次信息，对应数据库表 `link_batches`
- **链接数据(LINK_DATA)** - 存储轮询链接数据，对应数据库表 `link_data`
- 一个活码可关联多个链接数据，一个域名可用于多个活码

#### 访问记录
- **访问记录(ACCESS_RECORD)** - 存储短链接的访问信息，包括设备类型、操作系统、浏览器信息等，对应数据库表 `access_records`
- 每条访问记录关联到具体的活码和链接数据，包含IP归属地解析信息

#### 多类型链接轮询
- **链接批次(LINK_BATCH)** - 存储链接批次信息，包含批次管理和优先级控制，对应数据库表 `link_batches`
- **链接数据(LINK_DATA)** - 存储轮询的链接数据（WhatsApp、Telegram、网址），对应数据库表 `link_data`
- 一个活码可关联多个链接批次，每个批次包含多条链接数据

#### 操作审计
- **操作日志(OPERATION_LOG)** - 记录系统中的所有关键操作
- 关联操作用户，记录操作类型、对象和详情

#### 系统配置
- **系统配置(SYSTEM_CONFIG)** - 存储系统级配置参数，如默认跳转地址、IP解析服务配置等
- 记录配置更新人和更新时间，支持配置的追溯

---

## 版本对比

| 特性 | 企业级版本 | 简化版本 |
|------|------------|----------|
| 设计重点 | 性能优化、索引策略 | 业务逻辑清晰 |
| 实体命名 | 英文命名，规范化 | 中文命名，直观 |
| 字段设计 | 详细字段定义 | 核心字段展示 |
| 关系复杂度 | 复杂关系，多层级 | 简单关系，易理解 |
| 适用场景 | 开发实施 | 需求理解 |

## 建议使用

- **开发阶段**: 使用企业级版本进行数据库设计和优化
- **需求讨论**: 使用简化版本进行业务逻辑沟通
- **文档维护**: 两个版本同步更新，确保一致性