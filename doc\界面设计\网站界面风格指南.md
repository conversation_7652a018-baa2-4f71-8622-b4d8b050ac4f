# 网站界面风格指南

## 界面风格概览

```mermaid
flowchart TD
    subgraph "设计系统"
        A1["设计语言"] --> A1a["极简主义设计"]
        A1["设计语言"] --> A1b["扁平化风格"]
        A1["设计语言"] --> A1c["圆角矩形(0.625rem)"]
        A2["色彩方案"] --> A2a["黑白对比主色调"]
        A2["色彩方案"] --> A2b["功能性色彩区分"]
        A2["色彩方案"] --> A2c["亮色/暗色模式"]
        A3["组件系统"] --> A3a["模块化UI组件"]
        A3["组件系统"] --> A3b["统一组件样式"]
        A3["组件系统"] --> A3c["阴影与边框层级"]
    end

    subgraph "交互与布局"
        B1["排版布局"] --> B1a["左侧导航+右侧内容"]
        B1["排版布局"] --> B1b["卡片式内容展示"]
        B1["排版布局"] --> B1c["响应式设计"]
        B2["交互元素"] --> B2a["简洁按钮设计"]
        B2["交互元素"] --> B2b["精简表单元素"]
        B2["交互元素"] --> B2c["线性图标风格"]
        B3["字体排版"] --> B3a["无衬线字体"]
        B3["字体排版"] --> B3b["基础字号14px"]
        B3["字体排版"] --> B3c["清晰字体层级"]
    end

    D["现代简约管理系统界面"] --> A1
    D --> A2
    D --> A3
    D --> B1
    D --> B2
    D --> B3
```

## 页面布局示例

```mermaid
graph TD
    subgraph "页面结构"
        Main["主内容区域"]
        
        subgraph "侧边导航"
            Logo["品牌标识"]
            Nav1["主导航项"]
            Nav2["辅助导航项"]
            ThemeToggle["主题切换"]
        end
        
        subgraph "内容区域"
            Header["页面标题区"]
            Actions["操作按钮区"]
            
            subgraph "数据展示"
                Cards["卡片视图"]
                Table["表格视图"]
                Pagination["分页控件"]
            end
        end
        
        Main --- Header
        Header --- Actions
        Actions --- Cards
        Cards --- Table
        Table --- Pagination
    end
```

## 颜色系统

```mermaid
graph TD
    subgraph "颜色系统"
        direction LR
        A["基础色彩"] --> A1["黑白主色调"]
        A["基础色彩"] --> A2["功能色彩"]
        A["基础色彩"] --> A3["中性色调"]
        
        A2 --> A2a["成功绿"]
        A2 --> A2b["警告红"]
        A2 --> A2c["信息蓝"]
        
        B["模式切换"] --> B1["亮色模式"]
        B["模式切换"] --> B2["暗色模式"]
        
        B1 --> B1a["白底黑字"]
        B2 --> B2a["黑底白字"]
        
        C["色彩变量"] --> C1["--background"]
        C["色彩变量"] --> C2["--foreground"]
        C["色彩变量"] --> C3["--success"]
        C["色彩变量"] --> C4["--warning"]
        C["色彩变量"] --> C5["--info"]
        C["色彩变量"] --> C6["--muted"]
    end
```

## 设计语言

- **极简主义设计**：注重功能性和用户体验，减少不必要的装饰元素
- **扁平化风格**：避免拟物化和过度装饰，专注于内容本身
- **圆角设计**：统一使用圆角矩形（0.625rem），营造友好和谐的视觉感受
- **清晰层次**：通过留白、阴影和对比度建立明确的视觉层次结构
- **功能优先**：界面设计服务于功能，确保用户可以高效完成任务

## 色彩方案

- **黑白对比主色调**：亮色模式下白底黑字，暗色模式下黑底白字，强调内容可读性
- **功能性色彩区分**：
  - 成功状态：绿色
  - 警告/错误状态：红色
  - 信息/提示状态：蓝色
- **中性色调为主**：强调内容而非界面本身，减少视觉干扰
- **模式切换**：支持亮色/暗色模式无缝切换，提升不同环境下的用户体验

## 排版与布局

- **经典管理布局**：左侧固定导航栏 + 右侧内容区，清晰直观
- **卡片式内容**：使用卡片容器清晰分隔不同功能区域，增强信息组织性
- **合理留白**：充分利用留白和间距，提高可读性和内容区分度
- **响应式设计**：适应不同屏幕尺寸，确保在各种设备上的良好体验
- **网格系统**：基于CSS Grid和Flexbox的灵活网格布局，保持对齐和一致性

## 交互元素

- **按钮设计**：简洁明了，有明确的视觉反馈和状态变化（默认、悬浮、点击、禁用）
- **表单元素**：设计精简，注重易用性，提供清晰的输入反馈
- **微妙动效**：悬浮和点击效果自然流畅，不过分夸张，增强用户体验
- **线性图标**：采用简洁直观的线性图标风格，保持一致的视觉语言
- **即时反馈**：所有交互操作都有明确的视觉或功能反馈，减少用户疑惑

## 组件系统

- **模块化设计**：基于Shadcn UI组件库，确保设计一致性和可复用性
- **统一组件风格**：所有组件遵循相同的设计原则和视觉语言
- **组件层级关系**：通过微妙的阴影和边框区分组件层级，增强空间感
- **核心组件**：
  - **导航组件**：垂直侧边栏，支持折叠功能
  - **卡片组件**：内容分组和信息展示的主要容器
  - **表格组件**：数据展示，支持排序、筛选和分页
  - **对话框**：模态和非模态对话框，用于重要操作确认
  - **表单组件**：统一的输入控件样式和交互反馈

## 字体与排版

- **无衬线字体**：提高屏幕可读性，现代简洁
- **字体层级**：
  - 大标题：18-24px，加粗
  - 小标题：16px，加粗
  - 正文：14px，常规
  - 辅助文本：12px，常规或浅色
- **行高与间距**：合理的行高（约1.5倍字号）和字间距，提升阅读体验
- **文本对齐**：主要采用左对齐，确保阅读流畅性

## 响应式策略

- **移动优先**：设计思路从小屏幕出发，逐步扩展到大屏幕
- **断点设计**：设置合理的断点，确保各尺寸设备的良好体验
  - 移动端：< 768px
  - 平板：768px - 1024px
  - 桌面：> 1024px
- **内容适应**：关键内容在各种屏幕尺寸下都保持可访问和可用

## 设计原则

1. **一致性**：保持视觉和交互的一致性，降低用户学习成本
2. **简洁性**：减少视觉噪音，突出重要内容，避免过度设计
3. **可用性**：确保界面易于理解和使用，关注用户体验
4. **性能优化**：减少不必要的视觉效果，提高加载速度
5. **可维护性**：组件化设计，便于迭代和维护

## 示例页面结构

```
页面布局
├── 侧边导航栏
│   ├── 品牌标识
│   ├── 主导航项
│   ├── 辅助导航项
│   └── 主题切换开关
└── 主内容区
    ├── 页面标题区
    │   ├── 标题
    │   └── 操作按钮
    ├── 内容卡片
    │   ├── 卡片标题
    │   └── 卡片内容
    ├── 表格/列表视图
    │   ├── 筛选/搜索区
    │   ├── 数据展示
    │   └── 分页控件
    └── 状态反馈区
```

## 设计资源

- Shadcn UI组件库文档
- 色彩系统参考
- 线性图标库
- 设计规范检查清单 