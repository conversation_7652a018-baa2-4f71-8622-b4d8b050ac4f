# 短链接系统业务流程

## 1. 用户权限与层级管理流程

### 1.1 用户创建与权限分配
- **超级管理员创建用户**：
  - 超级管理员登录系统 → 进入"管理员列表"模块 → 点击"添加用户"按钮 → 填写用户信息（昵称、账号、密码、邮箱、角色选择"Boss"或"经理"）→ 提交创建
  - 系统自动记录操作日志，新用户即可登录系统
- **Boss创建下属经理**：
  - Boss登录系统 → 进入"管理员列表"模块 → 点击"添加用户"按钮 → 填写经理信息（角色固定为"经理"）→ 提交创建
  - 系统自动将新建经理关联至当前Boss（上级ID为当前Boss）

### 1.2 数据可见范围
- **超级管理员**：可查看系统内所有数据（活码、访问记录等）
- **Boss**：仅可查看自己及下属经理的数据（活码、访问记录）
- **经理**：仅可查看自己创建的数据（活码、访问记录）

## 2. 域名管理流程

### 2.1 域名添加与维护
- **超级管理员添加域名**：
  - 超级管理员登录系统 → 进入"域名管理"模块 → 点击"添加域名"按钮 → 填写域名信息（域名地址、有效期）→ 提交保存
  - 新添加域名默认标记为"在用"状态，对所有用户可见
- **域名有效期管理**：
  - 系统每月自动检查所有域名有效期
  - 对于过期域名，自动将状态从"在用"更新为"不可用"
  - "不可用"域名在下级用户（Boss、经理）的域名选择列表中被隐藏

## 3. 活码创建与管理流程

### 3.1 活码添加
- **用户创建活码**：
  - 用户（超级管理员/Boss/经理）登录系统 → 点击"添加活码"按钮 → 弹出活码添加表单
  - 填写活码信息：活码标题（必填）、选择前端域名（仅显示"在用"状态的域名）
  - 系统自动生成随机码（不可编辑）
  - 可选填写备注信息 → 点击"确定"提交
  - 系统保存活码信息，建立随机码与URL的关联关系

### 3.2 活码管理
- **查看活码列表**：
  - 用户登录系统 → 进入"短码管理"模块 → 系统根据用户权限加载对应活码列表
  - 超级管理员：显示全部活码
  - Boss：显示自己和下属经理的活码
  - 经理：仅显示自己创建的活码
- **活码编辑/删除**：
  - 在活码列表中，点击"修改"或"删除"按钮
  - 系统校验操作权限（用户只能操作有权限的活码）
  - 执行对应操作，更新数据库，记录操作日志

## 4. 短链接访问与跳转流程

### 4.1 短链接生成与跳转
- **短链接格式**：`https://域名/随机码`（如：`https://yourdomain.com/rJgMftSC`）
- **用户访问短链接**：
  - 外部用户点击短链接 → 系统提取URL中的随机码
  - 系统校验随机码有效性：
    - 有效（系统生成且已绑定链接）→ 跳转到绑定的目标链接
    - 无效（非系统生成或不存在）→ 跳转到Google首页
  - 系统记录访问信息：活码ID、访问时间、IP、解析IP位置

### 4.2 访问记录追踪
- **记录访问数据**：
  - 用户访问短链接时，系统自动记录访问信息
  - 调用IP归属地解析模块，获取访问者的地理位置
  - 将访问记录（含IP归属地信息）保存到数据库
- **查看访问记录**：
  - 用户在活码列表中点击"访问数"→ 跳转至访问记录模块
  - 系统加载该活码的访问记录（编号、访问时间、IP、IP位置等）
  - 用户可查看、批量删除访问记录（权限控制下）

## 5. 安全与审计流程

### 5.1 操作日志审计
- **系统记录关键操作**：
  - 用户管理（添加、修改、删除用户）
  - 活码操作（创建、修改、删除活码）
  - 域名管理等重要操作
- **日志查看**：
  - 超级管理员可查看全部日志
  - Boss可查看自己及下属经理的操作日志
  - 经理可查看自己的操作日志

### 5.2 账号安全管理
- **密码管理**：
  - 用户可自行修改密码（需验证原密码）
  - 上级可重置下属用户密码（生成临时密码）
- **登录安全**：
  - 系统记录用户登录信息（时间、IP、设备）
  - 用户可查看自己的登录记录，发现异常及时处理

## 6. 数据维护与更新流程

### 6.1 IP归属地数据更新
- **GeoLite2数据库更新**：
  - 系统每月自动检查并更新IP地理位置数据库
  - 更新在系统低峰期执行，确保解析准确性
- **历史数据重解析**：
  - 超级管理员可触发历史IP重新解析任务
  - 系统异步处理，提升历史数据准确性

### 6.2 统计分析应用
- **数据统计展示**：
  - 基于活码访问记录，生成多维度统计数据
  - 按地域、时间、设备类型等维度展示访问情况
  - 根据用户权限，展示对应范围的统计信息