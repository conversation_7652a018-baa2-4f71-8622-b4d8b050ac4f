# 活码系统项目架构设计

## 前端架构

### 技术选型

#### 核心框架
- **Vue 3**: 采用 Composition API，结合 `<script setup>` 语法
- **TypeScript**: 提供类型安全和开发体验
- **Vite**: 现代化的构建工具，提供快速的开发体验

#### UI组件库
- **Radix UI**: 无样式原语组件库
  - 提供高可访问性的基础交互组件
  - 专注于组件行为和可访问性，而非视觉样式
  - 为复杂交互提供可靠基础（如下拉菜单、模态框、弹出提示等）
  - 版本: 最新稳定版
  - 特点: 轻量级、无样式、关注交互和可访问性
  - 主要用途: 复杂交互组件的行为基础
  
- **Shadcn UI**: 基于Radix UI的高质量组件集合
  - 提供预设样式但保持可定制性
  - 组件可直接复制到项目中，便于定制
  - 支持主题系统，适应不同用户角色的视觉差异
  - 版本: 最新稳定版
  - 特点: 美观、可定制、组件丰富
  - 主要用途: 界面元素的视觉实现

#### 状态管理
- **Pinia**: Vue官方推荐的状态管理库
  - 按模块划分store
  - 支持TypeScript
  - 支持Vue开发者工具

#### 路由
- **Vue Router**: Vue官方路由库
  - 基于角色的路由访问控制
  - 懒加载优化页面加载性能

#### HTTP请求
- **Axios**: 请求库
  - 封装统一的请求拦截器和响应处理
  - 基于角色的API访问控制

### 架构设计

#### 分层架构
1. **视图层 (Views)**
   - 按用户角色组织：超级管理员、Boss、经理
   - 负责页面布局和组合组件

2. **组件层 (Components)**
   - UI组件: 基于Radix UI和Shadcn UI的基础组件
   - 布局组件: 页面布局相关组件
   - 业务组件: 特定业务逻辑的组件

3. **服务层 (Services)**
   - API服务: 封装后端API调用
   - 业务逻辑服务: 封装复杂业务逻辑

4. **状态管理层 (Store)**
   - 用户状态: 当前用户信息、权限等
   - 业务状态: 活码、访问记录等数据
   - UI状态: 主题、布局偏好等

#### 组件设计模式

1. **原子设计模式**
   - 原子 (Atoms): Radix UI原语组件
   - 分子 (Molecules): Shadcn UI组件和自定义组件
   - 组织 (Organisms): 业务组件组合
   - 模板 (Templates): 页面布局
   - 页面 (Pages): 完整页面

2. **组件通信**
   - Props/Events: 父子组件通信
   - Provide/Inject: 跨层级组件通信
   - Pinia Store: 全局状态管理
   - Composition API: 逻辑复用

3. **业务组件规范**
   - 活码相关组件统一使用 `ShortCode` 前缀
   - 组件命名采用 PascalCase
   - 文件命名与组件名保持一致

#### 主题和样式系统

1. **基于Shadcn UI的主题系统**
   - 使用CSS变量定义主题
   - 根据用户角色应用不同主题
     - 超级管理员: 紫色主题
     - Boss: 蓝色主题
     - 经理: 绿色主题

2. **响应式设计**
   - 移动优先设计
   - 基于断点的响应式布局
   - 适配不同尺寸的设备

#### 前端性能优化

1. **代码分割和懒加载**
   - 路由级别的代码分割
   - 组件异步加载

2. **渲染优化**
   - 虚拟滚动大数据表格
   - 合理使用v-memo减少重渲染

3. **缓存策略**
   - API响应缓存
   - 组件状态缓存

#### 权限控制

1. **菜单权限**
   - 基于角色动态渲染菜单
   - 控制菜单项可见性

2. **操作权限**
   - 控制按钮的可见性和可用性
   - 基于权限的条件渲染

3. **数据权限**
   - 控制数据可见范围
   - 根据用户角色过滤数据

## Radix UI 与 Shadcn UI 组件架构设计

### 组件封装策略

#### Radix UI 原语组件封装

1. **封装原则**
   - 保留原始可访问性特性
   - 添加Vue 3特有的功能（如Composition API集成）
   - 维持一致的API命名约定
   - 提供TypeScript类型支持

2. **核心原语组件**
   - **Dialog**: 对话框组件，用于活码添加/编辑
   - **DropdownMenu**: 下拉菜单，用于表格操作按钮
   - **Tabs**: 标签页切换，用于不同数据视图
   - **Toast**: 轻提示，用于操作反馈
   - **Select**: 选择器，用于表单选项
   - **Popover**: 弹出提示，用于帮助信息
   - **Toggle**: 开关，用于设置项
   - **ScrollArea**: 滚动区域，优化表格体验

3. **组件交互一致性**
   - 维持键盘导航一致性
   - 保持焦点管理机制
   - 统一动画效果
   - 确保屏幕阅读器支持

#### Shadcn UI 组件系统

1. **组件定制化**
   - 基于Radix UI原语构建
   - 添加预设样式
   - 支持主题变量覆盖
   - 支持组件变体

2. **核心UI组件**
   - **Button**: 按钮组件，支持多种变体
   - **Table/DataTable**: 表格组件，支持排序/筛选
   - **Card**: 卡片组件，用于数据展示
   - **Form**: 表单组件，集成验证
   - **Input**: 输入控件
   - **Badge**: 徽章组件，用于状态标识
   - **Sheet**: 侧边抽屉
   - **Avatar**: 头像组件

3. **组件变体系统**
   - 基础变体（默认、主要、次要、危险）
   - 尺寸变体（小、中、大）
   - 状态变体（加载中、禁用）
   - 角色特定变体（admin、boss、manager）

### 主题与样式架构

#### CSS变量系统

1. **基础变量层**
   ```css
   /* 基础主题变量 */
   :root {
     /* 颜色系统 */
     --color-primary: #1976d2;
     --color-primary-light: #42a5f5;
     --color-primary-dark: #0d47a1;
     
     /* 中性色 */
     --color-background: #ffffff;
     --color-surface: #f8f9fa;
     --color-text-primary: #202124;
     --color-text-secondary: #5f6368;
     
     /* 功能色 */
     --color-success: #4caf50;
     --color-error: #f44336;
     --color-warning: #ff9800;
     --color-info: #2196f3;
     
     /* 尺寸 */
     --spacing-unit: 4px;
     --radius-small: 4px;
     --radius-medium: 8px;
     --radius-large: 12px;
     
     /* 阴影 */
     --shadow-small: 0 2px 4px rgba(0, 0, 0, 0.1);
     --shadow-medium: 0 4px 8px rgba(0, 0, 0, 0.12);
     --shadow-large: 0 8px 16px rgba(0, 0, 0, 0.14);
     
     /* 字体 */
     --font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
     --font-size-small: 0.875rem;
     --font-size-base: 1rem;
     --font-size-large: 1.125rem;
     --font-weight-normal: 400;
     --font-weight-medium: 500;
     --font-weight-bold: 700;
     
     /* 动画 */
     --transition-fast: 0.15s ease;
     --transition-normal: 0.25s ease;
     --transition-slow: 0.35s ease;
   }
   ```

2. **角色特定主题**
   ```css
   /* 超级管理员主题 */
   .admin-theme {
     --color-primary: #5e35b1;
     --color-primary-light: #7e57c2;
     --color-primary-dark: #4527a0;
     --color-background: #f5f5f5;
     /* 更多覆盖... */
   }
   
   /* Boss主题 */
   .boss-theme {
     --color-primary: #1976d2;
     --color-primary-light: #42a5f5;
     --color-primary-dark: #0d47a1;
     /* 更多覆盖... */
   }
   
   /* 经理主题 */
   .manager-theme {
     --color-primary: #43a047;
     --color-primary-light: #66bb6a;
     --color-primary-dark: #2e7d32;
     --color-background: #ffffff;
     /* 更多覆盖... */
   }
   ```

3. **组件变量映射**
   ```css
   /* 按钮组件变量 */
   .button {
     --button-background: var(--color-primary);
     --button-text: #ffffff;
     --button-hover: var(--color-primary-dark);
     --button-active: var(--color-primary-dark);
     --button-focus-ring: rgba(var(--color-primary-rgb), 0.25);
     --button-padding-x: calc(var(--spacing-unit) * 4);
     --button-padding-y: calc(var(--spacing-unit) * 2);
     --button-radius: var(--radius-medium);
     /* 应用变量... */
   }
   ```

### 组件复合设计

#### 活码管理表格示例

**架构设计**:

```
ActivityCodeTable/ (组合组件)
├─ index.vue (主入口)
├─ ActivityCodeTableHeader.vue (表头组件)
│  ├─ 使用 Shadcn UI 的 Button 组件
│  └─ 使用 Radix UI 的 Tooltip 原语
├─ ActivityCodeTableFilters.vue (筛选组件)
│  ├─ 使用 Shadcn UI 的 Select 组件
│  └─ 使用 Radix UI 的 PopoverMenu 原语
├─ ActivityCodeList.vue (表格主体)
│  └─ 使用 Shadcn UI 的 DataTable 组件
└─ ActivityCodePagination.vue (分页组件)
   └─ 使用 Shadcn UI 的 Pagination 组件
```

**组件交互流**:
1. ActivityCodeTable 容器组件协调所有子组件
2. ActivityCodeTableHeader 提供"添加活码"按钮和批量操作
3. ActivityCodeTableFilters 提供筛选条件控制
4. ActivityCodeList 负责数据渲染和行交互
5. ActivityCodePagination 处理分页逻辑

#### 添加活码对话框示例

**架构设计**:

```
ActivityCodeDialog/ (组合组件)
├─ index.vue (对话框容器)
│  └─ 使用 Radix UI 的 Dialog 原语
├─ ActivityCodeForm.vue (表单组件)
│  ├─ 使用 Shadcn UI 的 Form 组件
│  └─ 使用 Shadcn UI 的 Input 组件
├─ DomainSelector.vue (域名选择器)
│  └─ 使用 Radix UI 的 Select 原语
└─ RandomCodeGenerator.vue (随机码生成器)
   └─ 使用 Shadcn UI 的 Button 组件
```

**组件交互流**:
1. ActivityCodeDialog 控制对话框状态
2. ActivityCodeForm 处理表单验证和提交
3. DomainSelector 提供域名选择功能
4. RandomCodeGenerator 处理随机码生成和显示

### 角色特定组件变体

#### 通过变体系统实现角色差异

**按钮组件变体**:

```vue
<script setup>
defineProps({
  variant: {
    type: String,
    default: 'default',
    validator: (value) => ['default', 'primary', 'secondary', 'danger'].includes(value)
  },
  size: {
    type: String,
    default: 'medium',
    validator: (value) => ['small', 'medium', 'large'].includes(value)
  },
  // 角色特定样式
  role: {
    type: String,
    default: '',
    validator: (value) => ['', 'admin', 'boss', 'manager'].includes(value)
  }
})
</script>

<template>
  <button 
    class="button" 
    :class="[
      `button-${variant}`,
      `button-${size}`,
      role ? `button-${role}` : ''
    ]"
  >
    <slot></slot>
  </button>
</template>

<style scoped>
.button {
  border-radius: var(--button-radius);
  font-weight: var(--button-font-weight);
  transition: background-color var(--transition-normal);
}

.button-primary {
  background-color: var(--color-primary);
  color: white;
}

.button-admin {
  /* 超级管理员特定样式 */
}

.button-boss {
  /* Boss特定样式 */
}

.button-manager {
  /* 经理特定样式 */
}
</style>
```

#### 表格组件的角色特定配置

```vue
<script setup>
import { computed } from 'vue';
import { useUserStore } from '@/stores/user';

const props = defineProps({
  activityCodes: Array
});

const userStore = useUserStore();

// 基于角色计算可见列
const columns = computed(() => {
  const baseColumns = [
    { key: 'id', title: '编号' },
    { key: 'title', title: '活码标题' },
    { key: 'shortUrl', title: '短链接' },
    { key: 'visits', title: '访问量' },
    { key: 'createdAt', title: '创建时间' },
    { key: 'actions', title: '操作' }
  ];
  
  // 超级管理员可以看到所有列
  if (userStore.role === 'admin') {
    return [
      ...baseColumns,
      { key: 'creator', title: '创建人' },
      { key: 'domainName', title: '域名' },
      { key: 'status', title: '状态' }
    ];
  }
  
  // Boss可以看到经理相关信息
  if (userStore.role === 'boss') {
    return [
      ...baseColumns,
      { key: 'creator', title: '创建人' }
    ];
  }
  
  // 经理只能看基本信息
  return baseColumns;
});

// 基于角色的操作按钮
const actions = computed(() => {
  if (userStore.role === 'admin') {
    return ['view', 'edit', 'delete', 'stats', 'transfer'];
  }
  
  if (userStore.role === 'boss') {
    return ['view', 'edit', 'delete', 'stats'];
  }
  
  return ['view', 'edit', 'delete'];
});
</script>

<template>
  <div class="table-container">
    <table class="data-table">
      <thead>
        <tr>
          <th v-for="column in columns" :key="column.key">{{ column.title }}</th>
        </tr>
      </thead>
      <tbody>
        <tr v-for="code in activityCodes" :key="code.id">
          <td v-for="column in columns" :key="`${code.id}-${column.key}`">
            <!-- 单元格内容根据列类型渲染 -->
            <template v-if="column.key === 'actions'">
              <button v-if="actions.includes('edit')" class="btn-edit">编辑</button>
              <button v-if="actions.includes('delete')" class="btn-delete">删除</button>
              <button v-if="actions.includes('stats')" class="btn-stats">统计</button>
              <button v-if="actions.includes('transfer')" class="btn-transfer">转移</button>
            </template>
            <template v-else>
              {{ code[column.key] }}
            </template>
          </td>
        </tr>
      </tbody>
    </table>
  </div>
</template>
```

## 后端架构

### 技术选型

- **Spring Boot**: 应用框架
- **Spring Data JPA**: ORM框架
- **Spring Security**: 安全框架
- **MySQL**: 关系型数据库
- **Redis**: 缓存和会话存储
- **MaxMind GeoLite2**: IP地理位置解析库

### 架构设计

#### 分层架构

1. **控制层 (Controller)**
   - REST API接口定义
   - 请求参数校验
   - 权限检查

2. **服务层 (Service)**
   - 业务逻辑实现
   - 事务管理
   - 轮询策略实现
   - IP地理位置解析

3. **数据访问层 (Repository)**
   - 数据库访问接口
   - 自定义查询
   - 批量操作支持

4. **实体层 (Entity)**
   - 数据库实体定义
   - ORM映射
   - 实体关联关系

5. **DTO层 (DTO)**
   - 数据传输对象
   - API请求和响应模型

#### 安全架构

1. **认证机制**
   - JWT令牌认证
   - 令牌刷新机制

2. **授权机制**
   - 基于角色的访问控制 (RBAC)
   - 数据权限控制

3. **安全防护**
   - SQL注入防护
   - XSS防护
   - CSRF防护

#### 缓存架构

1. **多级缓存**
   - 本地缓存: 减少Redis访问
   - Redis缓存: 分布式缓存

2. **缓存策略**
   - 热点数据缓存
   - 访问频率缓存

### 核心业务模块设计

#### 多类型链接轮询模块

1. **批次管理机制**
   - 批次创建: 每次添加链接数据时自动生成批次ID
   - 批次优先级: 后进先出原则，新批次优先级更高
   - 轮询次数控制: 可设置新批次的优先轮询次数

2. **轮询算法实现**
   ```java
   public class PollingService {
       // 根据活码ID获取下一个轮询数据
       public PollingData getNextPollingData(Long qrcodeId) {
           // 1. 查找是否有优先批次数据
           List<PollingData> priorityData = pollingDataRepository
               .findByQrcodeIdAndIsPriorityOrderByPriorityDescOrderNumAsc(qrcodeId, true);
           
           if (!priorityData.isEmpty()) {
               // 2. 使用优先批次中的第一条数据
               PollingData data = priorityData.get(0);
               
               // 3. 更新剩余轮询次数
               data.setRemainingCount(data.getRemainingCount() - 1);
               
               // 4. 如果剩余次数为0，将其移出优先批次
               if (data.getRemainingCount() <= 0) {
                   data.setIsPriority(false);
               }
               
               pollingDataRepository.save(data);
               return data;
           }
           
           // 5. 如果没有优先批次，使用常规轮询
           return getRegularPollingData(qrcodeId);
       }
       
       // 常规轮询（循环使用所有数据）
       private PollingData getRegularPollingData(Long qrcodeId) {
           // 实现轮询逻辑，如按orderNum顺序循环
           // ...
       }
   }
   ```

3. **URL拼接策略**
   - 根据数据类型动态选择拼接模板
   - WhatsApp: `https://api.whatsapp.com/send/?phone={value}`
   - Telegram: `https://t.me/{value}`
   - URL: 直接使用`{value}`

4. **批量操作支持**
   - 批量添加: 支持一次添加多条数据
   - 批量删除: 支持按ID或值批量删除
   - 全部删除: 支持清空活码关联的所有数据

#### 设备信息与用户代理解析模块

1. **User-Agent解析组件**
   ```java
   public class UserAgentParser {
       private final Parser uaParser;
       
       public UserAgentParser() {
           // 初始化User-Agent解析库
           this.uaParser = new Parser();
       }
       
       public DeviceInfo parseUserAgent(String userAgentString) {
           Client client = uaParser.parse(userAgentString);
           
           DeviceInfo deviceInfo = new DeviceInfo();
           deviceInfo.setDeviceType(determineDeviceType(client));
           deviceInfo.setOsInfo(client.os.family + " " + client.os.major);
           deviceInfo.setBrowserInfo(client.userAgent.family + " " + client.userAgent.major);
           deviceInfo.setUserAgent(userAgentString);
           
           return deviceInfo;
       }
       
       private String determineDeviceType(Client client) {
           // 根据设备信息判断类型（PC/移动/平板）
           // ...
       }
   }
   ```

2. **IP地理位置解析组件**
   ```java
   public class GeoLocationService {
       private final DatabaseReader reader;
       
       public GeoLocationService() throws IOException {
           // 初始化MaxMind GeoLite2数据库
           File database = new File("path/to/GeoLite2-City.mmdb");
           this.reader = new DatabaseReader.Builder(database).build();
       }
       
       public String resolveIpLocation(String ipAddress) {
           try {
               InetAddress address = InetAddress.getByName(ipAddress);
               CityResponse response = reader.city(address);
               
               String country = response.getCountry().getName();
               String region = response.getMostSpecificSubdivision().getName();
               String city = response.getCity().getName();
               
               return country + "-" + region + "-" + city;
           } catch (Exception e) {
               return "未知地区";
           }
       }
       
       // 定期更新GeoLite2数据库
       @Scheduled(cron = "0 0 0 1 * ?") // 每月1日凌晨执行
       public void updateGeoLiteDatabase() {
           // 实现数据库更新逻辑
           // ...
       }
   }
   ```

3. **访问记录增强**
   - 记录设备信息: 设备类型、操作系统、浏览器
   - 记录轮询数据: 关联使用的轮询数据ID
   - 支持多维度分析: 按设备类型、地域等筛选统计

## 微服务架构 (扩展规划)

未来系统规模扩大后，考虑拆分为以下微服务:

1. **用户认证服务**
   - 用户管理
   - 认证授权

2. **活码管理服务**
   - 活码CRUD
   - 短链接生成

3. **访问记录服务**
   - 访问日志记录
   - 统计分析
   - 设备信息解析
   - IP地理位置解析

4. **客户资源服务**
   - 客户管理
   - 资源分配

5. **域名管理服务**
   - 域名CRUD
   - 有效期管理

6. **轮询服务**
   - 多类型链接管理
   - 轮询策略实现
   - 批次优先级控制

## 前后端交互

### API设计

1. **RESTful API**
   - 资源命名规范
   - HTTP方法语义
   - 状态码使用规范

2. **统一响应格式**
```json
{
  "code": 200,
  "message": "success",
  "data": {}
}
```

### 数据交互流程

1. **前端请求流程**
   - 组件触发动作
   - 调用API服务
   - 更新状态
   - 渲染视图

2. **后端处理流程**
   - 接收请求
   - 验证权限
   - 执行业务逻辑
   - 返回响应

## UI组件体系

### Radix UI + Shadcn UI组件体系

1. **基础组件**
   - 原语组件: Radix UI提供的无样式组件
   - 样式组件: Shadcn UI提供的预设样式
   - 定制组件: 基于以上组件的项目特定定制

2. **组合组件**
   - 将多个基础组件组合成更复杂的UI模式
   - 保持一致的交互行为和视觉风格

3. **组件变体系统**
   - 通过变体系统扩展组件样式
   - 根据用户角色应用不同变体

4. **布局系统**
   - 使用Flex和Grid进行布局
   - 响应式布局适配不同设备

### 组件库结构

```
components/
├── ui/                # 基础UI组件 (Shadcn UI)
│   ├── button.vue     # 按钮组件
│   ├── table.vue      # 表格组件
│   ├── dialog.vue     # 对话框组件
│   └── ...
├── layout/            # 布局组件
│   ├── sidebar.vue    # 侧边栏组件
│   ├── header.vue     # 顶部栏组件
│   └── ...
└── business/          # 业务组件
    ├── short-code/    # 活码相关组件（统一命名）
    │   ├── ShortCodeList.vue         # 活码列表
    │   ├── ShortCodeForm.vue         # 活码表单
    │   ├── ShortCodeStats.vue        # 活码统计
    │   └── ShortCodeActions.vue      # 活码操作
    ├── user/          # 用户相关组件
    │   ├── UserList.vue              # 用户列表
    │   ├── UserForm.vue              # 用户表单
    │   └── UserPermissions.vue       # 用户权限
    ├── link-data/     # 链接数据组件（统一命名）
    │   ├── LinkDataList.vue          # 链接数据列表
    │   ├── LinkDataForm.vue          # 链接数据表单
    │   ├── BatchPriorityControl.vue  # 批次优先级控制
    │   └── LinkTypeSelector.vue      # 链接类型选择器
    └── access-record/ # 访问记录组件（统一命名）
        ├── AccessRecordList.vue      # 访问记录列表
        ├── DeviceInfoDisplay.vue     # 设备信息展示
        ├── GeoLocationMap.vue        # 地理位置地图
        └── AccessStatistics.vue      # 访问统计
```

### 业务组件示例

#### 轮询数据批次管理组件

```vue
<script setup>
import { ref, computed } from 'vue';
import { usePollingDataStore } from '@/stores/pollingData';

const props = defineProps({
  qrcodeId: {
    type: Number,
    required: true
  }
});

const pollingDataStore = usePollingDataStore();
const batches = ref([]);
const selectedBatch = ref(null);

// 获取批次数据
const fetchBatches = async () => {
  batches.value = await pollingDataStore.getBatchesByQrcodeId(props.qrcodeId);
};

// 调整批次优先级
const updateBatchPriority = async (batchId, priority, remainingCount) => {
  await pollingDataStore.updateBatchPriority(batchId, priority, remainingCount);
  fetchBatches();
};

// 计算批次状态
const getBatchStatus = (batch) => {
  if (batch.isPriority) {
    return `优先中 (剩余${batch.remainingCount}/${batch.totalCount}次)`;
  }
  return '常规轮询';
};

// 初始化
fetchBatches();
</script>

<template>
  <div class="batch-manager">
    <h3>批次管理</h3>
    
    <div class="batch-list">
      <div 
        v-for="batch in batches" 
        :key="batch.batchId"
        class="batch-item"
        :class="{ 'is-priority': batch.isPriority }"
        @click="selectedBatch = batch"
      >
        <div class="batch-header">
          <span class="batch-id">批次 #{{ batch.batchId }}</span>
          <span class="batch-status">{{ getBatchStatus(batch) }}</span>
        </div>
        <div class="batch-info">
          <div>创建时间: {{ new Date(batch.createdAt).toLocaleString() }}</div>
          <div>数据量: {{ batch.count }} 条</div>
          <div>优先级: {{ batch.priority }}</div>
        </div>
      </div>
    </div>
    
    <div v-if="selectedBatch" class="batch-editor">
      <h4>调整批次: {{ selectedBatch.batchId }}</h4>
      <div class="form-group">
        <label>优先级</label>
        <input type="number" v-model="selectedBatch.priority" />
      </div>
      <div class="form-group">
        <label>剩余轮询次数</label>
        <input type="number" v-model="selectedBatch.remainingCount" />
      </div>
      <div class="actions">
        <button 
          @click="updateBatchPriority(
            selectedBatch.batchId, 
            selectedBatch.priority, 
            selectedBatch.remainingCount
          )"
        >
          保存
        </button>
        <button @click="selectedBatch = null">取消</button>
      </div>
    </div>
  </div>
</template>
```

#### 设备信息展示组件

```vue
<script setup>
import { computed } from 'vue';

const props = defineProps({
  visitLog: {
    type: Object,
    required: true
  }
});

// 设备图标
const deviceIcon = computed(() => {
  const type = props.visitLog.deviceType.toLowerCase();
  if (type.includes('mobile')) return 'phone-icon';
  if (type.includes('tablet')) return 'tablet-icon';
  return 'desktop-icon';
});

// 操作系统图标
const osIcon = computed(() => {
  const os = props.visitLog.osInfo.toLowerCase();
  if (os.includes('windows')) return 'windows-icon';
  if (os.includes('mac') || os.includes('ios')) return 'apple-icon';
  if (os.includes('android')) return 'android-icon';
  return 'os-icon';
});

// 浏览器图标
const browserIcon = computed(() => {
  const browser = props.visitLog.browserInfo.toLowerCase();
  if (browser.includes('chrome')) return 'chrome-icon';
  if (browser.includes('firefox')) return 'firefox-icon';
  if (browser.includes('safari')) return 'safari-icon';
  if (browser.includes('edge')) return 'edge-icon';
  return 'browser-icon';
});
</script>

<template>
  <div class="device-info">
    <div class="device-type">
      <i :class="deviceIcon"></i>
      <span>{{ visitLog.deviceType }}</span>
    </div>
    <div class="os-info">
      <i :class="osIcon"></i>
      <span>{{ visitLog.osInfo }}</span>
    </div>
    <div class="browser-info">
      <i :class="browserIcon"></i>
      <span>{{ visitLog.browserInfo }}</span>
    </div>
  </div>
</template>
```

## 部署架构

### 开发环境

- 前端: Vite开发服务器
- 后端: 本地Spring Boot应用
- 数据库: 本地MySQL/H2

### 生产环境

- 前端: Nginx静态服务
- 后端: Docker容器化部署
- 数据库: MySQL主从架构
- 缓存: Redis集群

### CI/CD流程

1. 代码提交到Git仓库
2. 触发CI流程
   - 代码检查
   - 单元测试
   - 构建打包
3. 通过质量门后部署到测试环境
4. 测试通过后部署到生产环境

## 监控与日志

### 前端监控

- 性能监控: 页面加载时间、首次内容绘制等
- 错误监控: JS错误、API请求失败等
- 用户行为监控: 页面访问路径、交互事件等

### 后端监控

- 应用指标: QPS、响应时间、错误率等
- JVM指标: 内存使用、GC情况等
- 业务指标: 活码创建量、访问量等

### 日志系统

- 前端日志: 控制台日志、远程日志收集
- 后端日志: 应用日志、访问日志、错误日志
- 集中式日志: ELK架构收集分析日志 

### 业务监控指标

1. **活码性能指标**
   - 活码创建量: 每日/每周/每月新增活码数量
   - 活码访问量: 各活码访问次数统计
   - 跳转成功率: 成功跳转次数/总访问次数

2. **轮询效率指标**
   - 批次轮询分布: 各批次的轮询次数统计
   - 轮询响应时间: 获取轮询数据到完成跳转的时间
   - 数据使用均衡度: 各轮询数据使用次数的标准差

3. **设备与地域分析**
   - 设备类型分布: PC/移动/平板的访问比例
   - 操作系统分布: Windows/iOS/Android等系统占比
   - 浏览器分布: Chrome/Safari/Firefox等浏览器占比
   - 地域分布热力图: 基于IP解析的访问地域分布 