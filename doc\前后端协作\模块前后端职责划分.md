# 活码管理系统模块前后端职责划分

## 总体原则

1. **前端职责**
   - 用户界面实现
   - 数据展示与交互
   - 表单验证（客户端）
   - 状态管理
   - 路由控制
   - 错误提示与用户反馈

2. **后端职责**
   - 数据持久化
   - 业务逻辑处理
   - 安全验证
   - 数据验证（服务端）
   - API接口提供
   - 权限控制

3. **协作规范**
   - 接口遵循RESTful规范
   - 数据交换格式为JSON
   - 错误码统一定义
   - 接口文档及时更新
   - 版本控制一致

## 模块职责划分

### 1. 用户认证模块

#### 前端职责
- 实现登录表单与界面
- 表单数据验证（格式、必填项）
- 登录状态管理（保存token）
- 自动刷新token
- 登录失败提示
- 权限不足提示
- 登出功能

#### 后端职责
- 用户认证逻辑实现
- 密码加密与验证
- JWT令牌生成与验证
- 权限验证
- 登录日志记录
- 异常登录检测
- 提供登录、刷新token接口

### 2. 短码管理模块

#### 前端职责
- 实现短码列表展示
- 短码创建、编辑表单
- 短码状态切换UI
- 分页与筛选组件
- 短码复制功能
- 访问统计展示
- 根据用户角色动态显示操作按钮

#### 后端职责
- 短码数据CRUD接口
- 短码唯一性验证
- 短码生成算法
- 权限校验（数据所有权）
- 短码状态管理
- 访问统计更新
- 操作日志记录

### 3. 链接轮询模块

#### 前端职责
- 实现批次管理界面
- 链接数据添加表单
- 批次状态展示
- 链接数据列表展示
- 数据导入功能
- 批量操作UI
- 轮询状态可视化

#### 后端职责
- 批次数据CRUD接口
- 链接数据CRUD接口
- 轮询算法实现
- 批次优先级管理
- 轮询状态自动转换
- 轮询结果缓存
- 批量导入处理

### 4. 域名管理模块

#### 前端职责
- 实现域名列表展示
- 域名添加、编辑表单
- 域名状态展示
- 过期提醒展示
- 域名筛选功能
- 根据权限显示/隐藏

#### 后端职责
- 域名数据CRUD接口
- 域名唯一性验证
- 域名状态管理
- 过期检测与自动更新
- 权限控制（仅超级管理员）
- 域名与短码关联管理
- 操作日志记录

### 5. 用户管理模块

#### 前端职责
- 实现用户列表展示
- 用户创建、编辑表单
- 角色选择组件
- 用户状态切换UI
- 密码重置功能
- 上下级关系展示
- 根据角色动态显示操作按钮

#### 后端职责
- 用户数据CRUD接口
- 密码加密与重置
- 用户唯一性验证
- 角色权限管理
- 上下级关系管理
- 用户状态控制
- 操作日志记录

### 6. 访问记录模块

#### 前端职责
- 实现访问记录列表展示
- 多条件筛选组件
- 数据导出功能
- IP地址展示
- 地理位置展示
- 统计图表展示
- 分页控制

#### 后端职责
- 访问记录查询接口
- 访问记录保存逻辑
- IP地址解析服务
- 数据统计聚合
- 数据导出生成
- 大数据量分页优化
- 数据权限过滤

### 7. 操作日志模块

#### 前端职责
- 实现日志列表展示
- 多条件筛选组件
- 日志详情展示
- 操作类型展示
- 操作人信息展示
- 时间范围选择
- 分页控制

#### 后端职责
- 操作日志记录拦截器
- 日志查询接口
- 敏感信息脱敏
- 日志永久保存
- 数据权限过滤
- 日志导出功能
- 日志聚合统计

### 8. 短码跳转模块

#### 前端职责
- 实现跳转中间页（可选）
- 跳转失败提示页
- 加载动画（可选）

#### 后端职责
- 短码解析服务
- 跳转目标确定
- 访问记录保存
- 轮询算法执行
- 轮询计数更新
- 异常处理
- 性能优化

### 9. 数据统计模块

#### 前端职责
- 实现统计图表组件
- 多维度数据展示
- 时间范围选择器
- 数据筛选组件
- 图表交互功能
- 数据导出功能
- 自适应布局

#### 后端职责
- 统计数据聚合接口
- 多维度数据计算
- 大数据量处理优化
- 缓存机制实现
- 数据权限过滤
- 导出数据生成
- 定时统计任务

## 数据交互规范

### 数据格式

- 请求/响应均使用JSON格式
- 日期时间格式：ISO 8601（yyyy-MM-ddTHH:mm:ssZ）
- 分页参数统一：page（页码，从1开始）、size（每页条数）
- 排序参数统一：sort（字段名,asc/desc）

### 错误处理

- 前端负责展示友好的错误提示
- 后端提供统一的错误码和错误信息
- 前端根据错误码进行相应处理
- 网络错误由前端统一处理
- 业务逻辑错误由后端明确提供错误信息

### 权限控制

- 后端负责权限验证和数据过滤
- 前端根据用户角色动态调整界面
- 敏感操作需前后端双重验证
- 数据权限由后端严格控制
- 前端不能信任客户端数据

## 性能优化

### 前端优化

- 组件懒加载
- 数据缓存
- 虚拟滚动
- 防抖与节流
- 资源压缩
- 按需加载

### 后端优化

- 数据库查询优化
- 缓存机制
- 分页查询
- 异步处理
- 并发控制
- 资源池化

## 安全措施

### 前端安全

- 输入验证
- XSS防护
- CSRF Token使用
- 敏感信息不在前端存储
- 禁止内联脚本

### 后端安全

- 参数验证
- SQL注入防护
- 权限严格校验
- 敏感数据加密
- 速率限制
- 日志审计 